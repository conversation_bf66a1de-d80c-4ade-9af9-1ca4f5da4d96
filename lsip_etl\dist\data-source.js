"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("dotenv/config");
const typeorm_1 = require("typeorm");
const sslMode = process.env.DB_SSL_MODE ?? 'require';
const schema = process.env.DB_SCHEMA;
const sslOption = (() => {
    if (!sslMode || sslMode === 'disable') {
        return false;
    }
    if (sslMode === 'verify-full') {
        return { rejectUnauthorized: true };
    }
    return { rejectUnauthorized: false };
})();
if (!schema || schema.trim() === '') {
    throw new Error('DB_SCHEMA environment variable must be provided');
}
const fileExtension = __filename.slice(__filename.lastIndexOf('.'));
const isRunningTs = fileExtension === '.ts';
const AppDataSource = new typeorm_1.DataSource({
    type: 'postgres',
    host: process.env.DB_HOST ?? 'localhost',
    port: Number(process.env.DB_PORT ?? 5432),
    username: process.env.DB_USER ?? 'postgres',
    password: process.env.DB_PASS ?? '',
    database: process.env.DB_NAME ?? 'postgres',
    ssl: sslOption,
    schema,
    extra: {
        options: `-c search_path=${schema}`,
    },
    entities: [],
    migrations: isRunningTs ? ['src/migrations/*.ts'] : ['dist/migrations/*.js'],
    migrationsTableName: 'migrations',
});
exports.default = AppDataSource;
//# sourceMappingURL=data-source.js.map