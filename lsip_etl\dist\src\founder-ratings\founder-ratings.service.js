"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FounderRatingsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_founder_rating_entity_1 = require("./jvs-founder-rating.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
let FounderRatingsService = class FounderRatingsService {
    founderRatingRepository;
    accountRepository;
    constructor(founderRatingRepository, accountRepository) {
        this.founderRatingRepository = founderRatingRepository;
        this.accountRepository = accountRepository;
    }
    async upsertFounderRatings(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedAccountIds = Array.from(new Set(records.map((record) => record.account__c).filter((value) => Boolean(value))));
        const existingAccountIds = requestedAccountIds.length
            ? new Set((await this.accountRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedAccountIds) },
            })).map((account) => account.id))
            : new Set();
        const entities = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                accountAttempted: null,
            };
            const accountId = record.account__c && existingAccountIds.has(record.account__c) ? record.account__c : null;
            if (record.account__c && !accountId) {
                meta.accountAttempted = record.account__c;
                meta.issues.push(`Account ${record.account__c} not found; stored without account linkage.`);
            }
            const entity = this.founderRatingRepository.create({
                id: record.id,
                name: record.name ?? null,
                createdDate: record.createddate ? new Date(record.createddate) : null,
                accountId,
                visionStrategicThinking: record.vision_strategic_thinking__c ?? null,
                adaptabilityLearnability: record.adaptability_learnability__c ?? null,
                goalOrientation: record.goal_orientation__c ?? null,
                orgBuildingAbilityToAttractHighQ: record.org_building_ability_to_attract_high_q__c ?? null,
                narrativeBuildingStoryTelling: record.narrative_building_story_telling__c ?? null,
                financialPrudenceBurnManagement: record.financial_prudence_burn_management__c ?? null,
                paceOfExecutionBiasToAction: record.pace_of_execution_bias_to_action__c ?? null,
                abilityToTakeToughDecisions: record.ability_to_take_tough_decisions__c ?? null,
                leadingOthersOutcomeFromTeam: record.leading_others_outcome_from_team__c ?? null,
                selfAwareness: record.self_awareness__c ?? null,
                integrityTransparency: record.integrity_transparency__c ?? null,
                ratingDate: record.rating_date__c ? new Date(record.rating_date__c) : null,
            });
            metaById.set(record.id, meta);
            entities.push(entity);
        }
        if (entities.length > 0) {
            await this.founderRatingRepository.save(entities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    accountAttempted: meta.accountAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.FounderRatingsService = FounderRatingsService;
exports.FounderRatingsService = FounderRatingsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_founder_rating_entity_1.JvsFounderRating)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], FounderRatingsService);
//# sourceMappingURL=founder-ratings.service.js.map