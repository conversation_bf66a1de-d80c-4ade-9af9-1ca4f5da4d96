export declare class KpiPerticularRecordDto {
    id: string;
    name?: string;
    currencyisocode?: string;
    business_unit__c?: string;
    company__c?: string;
    creation_type__c?: string;
    formulaid__c?: string;
    formula__c?: string;
    kpi_master__c?: string;
    kpi_particular_level_number__c?: number;
    kpi_particular_level__c?: string;
    name_set__c?: boolean;
    name__c?: string;
    plan_required__c?: boolean;
    sub_business_unit__c?: string;
    unique_identifier__c?: string;
    benchmark__c?: string;
    industry__c?: string;
    kpi_particular_type__c?: string;
    sub_industry__c?: string;
    entity_id__c?: string;
}
export declare class UpsertKpiPerticularDto {
    KpiPerticulars: KpiPerticularRecordDto[];
}
export type KpiPerticularRecord = KpiPerticularRecordDto;
