"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorPlanActualController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const vector_plan_actual_service_1 = require("./vector-plan-actual.service");
const upsert_vector_plan_actual_dto_1 = require("./dto/upsert-vector-plan-actual.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const vector_plan_actual_response_dto_1 = require("./dto/vector-plan-actual-response.dto");
let VectorPlanActualController = class VectorPlanActualController {
    vectorPlanActualService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(vectorPlanActualService) {
        this.vectorPlanActualService = vectorPlanActualService;
    }
    async upsertVectorPlanActuals(payload) {
        const result = await this.vectorPlanActualService.upsertVectorPlanActuals(payload.VectorPlanActuals);
        return (0, api_response_dto_1.createSuccessResponse)('Vector plan actual records processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.VectorPlanActualController = VectorPlanActualController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(VectorPlanActualController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert vector plan actual records.' }),
    (0, swagger_1.ApiBody)({ type: upsert_vector_plan_actual_dto_1.UpsertVectorPlanActualDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Vector plan actual records processed successfully.',
        type: vector_plan_actual_response_dto_1.VectorPlanActualApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_vector_plan_actual_dto_1.UpsertVectorPlanActualDto]),
    __metadata("design:returntype", Promise)
], VectorPlanActualController.prototype, "upsertVectorPlanActuals", null);
exports.VectorPlanActualController = VectorPlanActualController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('vector-plan-actuals'),
    __metadata("design:paramtypes", [vector_plan_actual_service_1.VectorPlanActualService])
], VectorPlanActualController);
//# sourceMappingURL=vector-plan-actual.controller.js.map