"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJvsAccountsTable************* = void 0;
class CreateJvsAccountsTable************* {
    name = 'CreateJvsAccountsTable*************';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_accounts (
              id character varying(18) NOT NULL,
              masterrecordid character varying(18),
              name character varying(255),
              type character varying(255),
              parentid character varying(18),
              phone character varying(40),
              website character varying(255),
              industry character varying(255),
              description character varying(32000),
              currencyisocode character varying(3),
              ispartner text,
              board_position_held__c character varying(40),
              company_full_name__c character varying(255),
              going_in_cost__c text,
              going_in_post_money_value__c text,
              going_in_pre_money__c text,
              hq_geography__c character varying(255),
              initial_investment_stage__c character varying(255),
              initial_ownership__c text,
              initial_revenue_stage__c character varying(255),
              initial_round__c character varying(255),
              lsip_initial_financing_roung__c character varying(255),
              lead__c character varying(20),
              legal_entity_name__c character varying(255),
              legal_geography__c character varying(255),
              sector__c character varying(255),
              short_name__c character varying(20),
              x2nd_lead__c character varying(20),
              documentation__c character varying(255),
              company_status__c character varying(255),
              region__c character varying(255),
              currency_format__c character varying(255),
              industry_master__c character varying(18),
              sub_industry__c character varying(18),
              region_country__c character varying(255),
              auditor__c character varying(255),
              benchmark__c character varying(255),
              entity__c character varying(4099),
              business__c character varying(255),
              CONSTRAINT pk_jvs_accounts_id PRIMARY KEY (id)
            );
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_accounts_masterrecordid ON jvs_accounts (masterrecordid);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_accounts_parentid ON jvs_accounts (parentid);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_accounts_industry_master__c ON jvs_accounts (industry_master__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_accounts_sub_industry__c ON jvs_accounts (sub_industry__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_accounts_short_name__c ON jvs_accounts (short_name__c);
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_accounts ADD CONSTRAINT fk_jvs_accounts_parentid FOREIGN KEY (parentid) REFERENCES jvs_accounts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_accounts ADD CONSTRAINT fk_jvs_accounts_industry_master__c FOREIGN KEY (industry_master__c) REFERENCES jvs_industries(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_accounts ADD CONSTRAINT fk_jvs_accounts_sub_industry__c FOREIGN KEY (sub_industry__c) REFERENCES jvs_industries(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE jvs_accounts DROP CONSTRAINT IF EXISTS fk_jvs_accounts_sub_industry__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_accounts DROP CONSTRAINT IF EXISTS fk_jvs_accounts_industry_master__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_accounts DROP CONSTRAINT IF EXISTS fk_jvs_accounts_parentid;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_accounts_short_name__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_accounts_sub_industry__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_accounts_industry_master__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_accounts_parentid;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_accounts_masterrecordid;
    `);
        await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_accounts;
    `);
    }
}
exports.CreateJvsAccountsTable************* = CreateJvsAccountsTable*************;
//# sourceMappingURL=*************-CreateJvsAccountsTable.js.map