import { SyncedRecordsQueryDto } from './dto/records-summary.dto';
import { RecordsService } from './records.service';
export declare class RecordsController {
    private readonly recordsService;
    constructor(recordsService: RecordsService);
    getRecordsSummary(): Promise<import("../common/dto/api-response.dto").ApiResponse<import("./dto/records-summary.dto").RecordsSummaryDataDto>>;
    getSyncedRecords(query: SyncedRecordsQueryDto): Promise<import("../common/dto/api-response.dto").ApiResponse<import("./dto/records-summary.dto").SyncedRecordsDataDto>>;
}
