"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsBusinessRating = void 0;
const typeorm_1 = require("typeorm");
let JvsBusinessRating = class JvsBusinessRating {
    id;
    name = null;
    createdDate = null;
    accountId = null;
    productVisionStrategy = null;
    productVisionStrategyDescription = null;
    gtmMuscleEntSalesPartnershipDescription = null;
    gtmMuscleEntSalesPartnership = null;
    dataTechnologyOrientation = null;
    dataTechnologyOrientationDescription = null;
    deepTechEnggProductLeadership = null;
    deepTechEnggProductLeadershipDescription = null;
    gtmMuscleCommercialization = null;
    gtmMuscleCommercializationDescription = null;
    regulatory = null;
    regulatoryDescription = null;
    productVisionStrategyAiMl = null;
    productVisionStrategyAiMlDescription = null;
    gtmMuscleForDevtoolsProsumer = null;
    gtmMuscleForDevtoolsProsumerDescription = null;
    narrativeBuilding = null;
    narrativeBuildingDescription = null;
    productVisionStrategyAiLlm = null;
    productVisionStrategyAiLlmDescription = null;
    gtmMuscle = null;
    gtmMuscleDescription = null;
    positioningNarrativeBuilding = null;
    positioningNarrativeBuildingDescription = null;
    productStrategyStrategicThinking = null;
    productStrategyStrategicThinkingDescription = null;
    marketingNarrativeBuilding = null;
    marketingNarrativeBuildingDescription = null;
    aiCapability = null;
    aiCapabilityDescription = null;
    categoryCreation = null;
    categoryCreationDescription = null;
};
exports.JvsBusinessRating = JvsBusinessRating;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsBusinessRating.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'createddate', type: 'timestamptz', nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "createdDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'account__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "accountId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_vision_strategy__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "productVisionStrategy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_vision_strategy_des__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "productVisionStrategyDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gtm_muscle_ent_sales_partnership_des__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "gtmMuscleEntSalesPartnershipDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gtm_muscle_ent_sales_partnership__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "gtmMuscleEntSalesPartnership", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'data_technology_orientation__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "dataTechnologyOrientation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'data_technology_orientation_des__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "dataTechnologyOrientationDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'deep_tech_engg_product_leadership__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "deepTechEnggProductLeadership", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'deep_tech_engg_product_leadership_des__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "deepTechEnggProductLeadershipDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gtm_muscle_commercialization__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "gtmMuscleCommercialization", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gtm_muscle_commercialization_des__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "gtmMuscleCommercializationDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'regulatory__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "regulatory", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'regulatory_des__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "regulatoryDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_vision_strategy_ai_ml__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "productVisionStrategyAiMl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_vision_strategy_ai_ml_des__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "productVisionStrategyAiMlDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gtm_muscle_for_devtools_prosumer__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "gtmMuscleForDevtoolsProsumer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gtm_muscle_for_devtools_prosumer_des__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "gtmMuscleForDevtoolsProsumerDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'narrative_building__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "narrativeBuilding", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'narrative_building_desc__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "narrativeBuildingDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_vision_strategy_ai_llm__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "productVisionStrategyAiLlm", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_vision_strategy_ai_llm_desc__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "productVisionStrategyAiLlmDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gtm_muscle__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "gtmMuscle", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gtm_muscle_desc__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "gtmMuscleDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'positioning_narrative_building__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "positioningNarrativeBuilding", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'positioning_narrative_building_desc__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "positioningNarrativeBuildingDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_strategy_strategic_thinking__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "productStrategyStrategicThinking", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_strategy_strategic_thinking_des__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "productStrategyStrategicThinkingDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketing_narrative_building__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "marketingNarrativeBuilding", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketing_narrative_building_desc__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "marketingNarrativeBuildingDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ai_capability__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "aiCapability", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ai_capability_desc__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "aiCapabilityDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'category_creation__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "categoryCreation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'category_creation_desc__c', type: 'varchar', length: 4000, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessRating.prototype, "categoryCreationDescription", void 0);
exports.JvsBusinessRating = JvsBusinessRating = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_business_ratings' })
], JvsBusinessRating);
//# sourceMappingURL=jvs-business-rating.entity.js.map