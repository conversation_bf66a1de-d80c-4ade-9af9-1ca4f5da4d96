"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorDetailModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const vector_detail_controller_1 = require("./vector-detail.controller");
const vector_detail_service_1 = require("./vector-detail.service");
const jvs_vector_detail_entity_1 = require("./jvs-vector-detail.entity");
const jvs_kpi_perticular_entity_1 = require("../kpi-perticular/jvs-kpi-perticular.entity");
const jvs_vector_master_entity_1 = require("../vector-master/jvs-vector-master.entity");
let VectorDetailModule = class VectorDetailModule {
};
exports.VectorDetailModule = VectorDetailModule;
exports.VectorDetailModule = VectorDetailModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_vector_detail_entity_1.JvsVectorDetail, jvs_kpi_perticular_entity_1.JvsKpiPerticular, jvs_vector_master_entity_1.JvsVectorMaster])],
        controllers: [vector_detail_controller_1.VectorDetailController],
        providers: [vector_detail_service_1.VectorDetailService],
        exports: [vector_detail_service_1.VectorDetailService],
    })
], VectorDetailModule);
//# sourceMappingURL=vector-detail.module.js.map