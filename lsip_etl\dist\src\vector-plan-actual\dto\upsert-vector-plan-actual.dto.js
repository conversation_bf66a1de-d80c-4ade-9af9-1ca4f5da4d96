"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertVectorPlanActualDto = exports.VectorPlanActualRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class VectorPlanActualRecordDto {
    id;
    name;
    currencyIsoCode;
    actual;
    month1;
    alternateName;
    numberOfPlanRevisions;
    plan;
    quarter;
    reasonForDeviationRemarks;
    uniqueIdentifier;
    vectorDetailId;
    year;
    monthNumber;
    consolidatedDate;
    kpiPlanActualId;
    planActualDate;
}
exports.VectorPlanActualRecordDto = VectorPlanActualRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the vector plan actual record.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Display name.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Currency ISO code.', maxLength: 3 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(3),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "currencyIsoCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Actual value.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "actual", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Month indicator string.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "month1", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Alternate name.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "alternateName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of plan revisions.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], VectorPlanActualRecordDto.prototype, "numberOfPlanRevisions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Plan value.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "plan", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Quarter label.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "quarter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Reason for deviation remarks.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "reasonForDeviationRemarks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Unique identifier string.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "uniqueIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Vector detail identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(18),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "vectorDetailId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Fiscal year.', maxLength: 4 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "year", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Month number.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], VectorPlanActualRecordDto.prototype, "monthNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Consolidated date string.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "consolidatedDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'KPI plan actual identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(18),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "kpiPlanActualId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Plan/actual date.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], VectorPlanActualRecordDto.prototype, "planActualDate", void 0);
class UpsertVectorPlanActualDto {
    VectorPlanActuals;
}
exports.UpsertVectorPlanActualDto = UpsertVectorPlanActualDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [VectorPlanActualRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => VectorPlanActualRecordDto),
    __metadata("design:type", Array)
], UpsertVectorPlanActualDto.prototype, "VectorPlanActuals", void 0);
//# sourceMappingURL=upsert-vector-plan-actual.dto.js.map