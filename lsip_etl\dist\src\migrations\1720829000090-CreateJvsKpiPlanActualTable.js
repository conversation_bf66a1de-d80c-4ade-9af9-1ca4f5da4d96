"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJvsKpiPlanActualTable1720829000090 = void 0;
class CreateJvsKpiPlanActualTable1720829000090 {
    name = 'CreateJvsKpiPlanActualTable1720829000090';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_kpi_plan_actual (
              id character varying(18) NOT NULL,
              name character varying(80),
              actual__c character varying(255),
              kpi_particular__c character varying(18),
              month1__c character varying(255),
              name__c character varying(255),
              number_of_plan_revisions__c double precision,
              plan__c character varying(255),
              quarter__c character varying(255),
              reason_for_deviation_remarks__c character varying(255),
              year__c character varying(4),
              actual_formula__c character varying(1300),
              month_number__c double precision,
              percentage_of_achievement__c double precision,
              consolidated_date__c character varying(255),
              plan_actual_date__c date,
              CONSTRAINT pk_jvs_kpi_plan_actual_id PRIMARY KEY (id)
            );
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_plan_actual_kpi_particular__c ON jvs_kpi_plan_actual (kpi_particular__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_plan_actual_plan_actual_date__c ON jvs_kpi_plan_actual (plan_actual_date__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_plan_actual_year__c ON jvs_kpi_plan_actual (year__c);
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_plan_actual ADD CONSTRAINT fk_jvs_kpi_plan_actual_kpi_particular__c FOREIGN KEY (kpi_particular__c) REFERENCES jvs_kpi_perticular(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_plan_actual DROP CONSTRAINT IF EXISTS fk_jvs_kpi_plan_actual_kpi_particular__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_plan_actual_year__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_plan_actual_plan_actual_date__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_plan_actual_kpi_particular__c;
    `);
        await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_kpi_plan_actual;
    `);
    }
}
exports.CreateJvsKpiPlanActualTable1720829000090 = CreateJvsKpiPlanActualTable1720829000090;
//# sourceMappingURL=1720829000090-CreateJvsKpiPlanActualTable.js.map