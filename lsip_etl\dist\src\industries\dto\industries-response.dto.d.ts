import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { IndustryRecordDto } from './upsert-industries.dto';
export declare class UnprocessedIndustryRecordDto extends IndustryRecordDto {
    message: string;
    parentIndustryAttempted: string | null;
}
export declare class IndustriesProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedIndustryRecordDto[];
}
export declare class IndustriesApiResponseDto extends BaseApiResponseDto {
    data: IndustriesProcessedDataDto;
}
