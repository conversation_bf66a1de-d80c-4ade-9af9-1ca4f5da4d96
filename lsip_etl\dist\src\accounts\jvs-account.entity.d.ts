export declare class JvsAccount {
    id: string;
    masterRecordId: string | null;
    name: string | null;
    type: string | null;
    parentId: string | null;
    phone: string | null;
    website: string | null;
    industry: string | null;
    description: string | null;
    currencyIsoCode: string | null;
    isPartner: boolean | null;
    boardPositionHeld: string | null;
    companyFullName: string | null;
    goingInCost: string | null;
    goingInPostMoneyValue: string | null;
    goingInPreMoney: string | null;
    hqGeography: string | null;
    initialInvestmentStage: string | null;
    initialOwnership: string | null;
    initialRevenueStage: string | null;
    initialRound: string | null;
    lsipInitialFinancingRound: string | null;
    lead: string | null;
    legalEntityName: string | null;
    legalGeography: string | null;
    sector: string | null;
    shortName: string | null;
    secondLead: string | null;
    documentation: string | null;
    companyStatus: string | null;
    region: string | null;
    currencyFormat: string | null;
    industryMasterId: string | null;
    subIndustryId: string | null;
    regionCountry: string | null;
    auditor: string | null;
    benchmark: string | null;
    entity: string | null;
    business: string | null;
    year?: string | null;
}
