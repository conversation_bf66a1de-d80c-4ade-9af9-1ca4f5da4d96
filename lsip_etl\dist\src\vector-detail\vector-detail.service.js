"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorDetailService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_vector_detail_entity_1 = require("./jvs-vector-detail.entity");
const jvs_kpi_perticular_entity_1 = require("../kpi-perticular/jvs-kpi-perticular.entity");
const jvs_vector_master_entity_1 = require("../vector-master/jvs-vector-master.entity");
let VectorDetailService = class VectorDetailService {
    vectorDetailRepository;
    kpiPerticularRepository;
    vectorMasterRepository;
    constructor(vectorDetailRepository, kpiPerticularRepository, vectorMasterRepository) {
        this.vectorDetailRepository = vectorDetailRepository;
        this.kpiPerticularRepository = kpiPerticularRepository;
        this.vectorMasterRepository = vectorMasterRepository;
    }
    async upsertVectorDetails(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedKpiPerticularIds = new Set(records.map((record) => record.kpiPerticularId).filter((value) => Boolean(value)));
        const requestedVectorMasterIds = new Set(records.map((record) => record.vectorMasterId).filter((value) => Boolean(value)));
        const existingKpiPerticularIds = requestedKpiPerticularIds.size
            ? new Set((await this.kpiPerticularRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedKpiPerticularIds]) },
            })).map((particular) => particular.id))
            : new Set();
        const existingVectorMasterIds = requestedVectorMasterIds.size
            ? new Set((await this.vectorMasterRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedVectorMasterIds]) },
            })).map((master) => master.id))
            : new Set();
        const entities = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                kpiPerticularAttempted: null,
                vectorMasterAttempted: null,
            };
            const kpiPerticularId = record.kpiPerticularId && existingKpiPerticularIds.has(record.kpiPerticularId)
                ? record.kpiPerticularId
                : null;
            if (record.kpiPerticularId && !kpiPerticularId) {
                meta.kpiPerticularAttempted = record.kpiPerticularId;
                meta.issues.push(`KPI particular ${record.kpiPerticularId} not found; stored without KPI particular linkage.`);
            }
            const vectorMasterId = record.vectorMasterId && existingVectorMasterIds.has(record.vectorMasterId)
                ? record.vectorMasterId
                : null;
            if (record.vectorMasterId && !vectorMasterId) {
                meta.vectorMasterAttempted = record.vectorMasterId;
                meta.issues.push(`Vector master ${record.vectorMasterId} not found; stored without vector master linkage.`);
            }
            const entity = this.vectorDetailRepository.create({
                id: record.id,
                name: record.name ?? null,
                currencyIsoCode: record.currencyIsoCode ?? null,
                kpiPerticularId,
                alternateName: record.alternateName ?? null,
                uniqueIdentifier: record.uniqueIdentifier ?? null,
                vectorMasterId,
            });
            metaById.set(record.id, meta);
            entities.push(entity);
        }
        if (entities.length > 0) {
            await this.vectorDetailRepository.save(entities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    kpiPerticularAttempted: meta.kpiPerticularAttempted,
                    vectorMasterAttempted: meta.vectorMasterAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.VectorDetailService = VectorDetailService;
exports.VectorDetailService = VectorDetailService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_vector_detail_entity_1.JvsVectorDetail)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_kpi_perticular_entity_1.JvsKpiPerticular)),
    __param(2, (0, typeorm_1.InjectRepository)(jvs_vector_master_entity_1.JvsVectorMaster)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], VectorDetailService);
//# sourceMappingURL=vector-detail.service.js.map