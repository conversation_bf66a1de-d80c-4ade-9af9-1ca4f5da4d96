"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsContact = void 0;
const typeorm_1 = require("typeorm");
let JvsContact = class JvsContact {
    id;
    isDeleted = null;
    masterRecordId = null;
    accountId = null;
    lastName = null;
    firstName = null;
    salutation = null;
    middleName = null;
    name = null;
    phone = null;
    fax = null;
    mobilePhone = null;
    assistantPhone = null;
    reportsToId = null;
    email = null;
    title = null;
    department = null;
    assistantName = null;
    birthDate = null;
    hasOptedOutOfEmail = null;
    hasOptedOutOfFax = null;
    doNotCall = null;
    titleType = null;
    departmentGroup = null;
    companyFundId = null;
    dayOfMonth = null;
    designationRank = null;
    dateOfBirth = null;
    director = null;
    department2 = null;
    designation = null;
    keyContact = null;
    rating = null;
};
exports.JvsContact = JvsContact;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsContact.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'isdeleted', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "isDeleted", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'masterrecordid', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "masterRecordId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'accountid', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "accountId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lastname', type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "lastName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'firstname', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "firstName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'salutation', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "salutation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'middlename', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "middleName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'name', type: 'varchar', length: 121, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'phone', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fax', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "fax", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mobilephone', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "mobilePhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assistantphone', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "assistantPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reportstoid', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "reportsToId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'email', type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'title', type: 'varchar', length: 128, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'department', type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "department", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assistantname', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "assistantName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'birthdate', type: 'date', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "birthDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'hasoptedoutofemail', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "hasOptedOutOfEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'hasoptedoutoffax', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "hasOptedOutOfFax", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'donotcall', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "doNotCall", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'titletype', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "titleType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'departmentgroup', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "departmentGroup", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company_fund__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "companyFundId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'dayofmonth__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "dayOfMonth", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'designation_rank__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "designationRank", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'dob__c', type: 'date', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "dateOfBirth", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'director__c', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "director", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'department_2__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "department2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'designation__c', type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "designation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'key_contact__c', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "keyContact", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'rating__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsContact.prototype, "rating", void 0);
exports.JvsContact = JvsContact = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_contacts' })
], JvsContact);
//# sourceMappingURL=jvs-contact.entity.js.map