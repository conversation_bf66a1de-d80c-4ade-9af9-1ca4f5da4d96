"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiPerticularApiResponseDto = exports.KpiPerticularProcessedDataDto = exports.UnprocessedKpiPerticularRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_kpi_perticular_dto_1 = require("./upsert-kpi-perticular.dto");
class UnprocessedKpiPerticularRecordDto extends upsert_kpi_perticular_dto_1.KpiPerticularRecordDto {
    message;
    accountAttempted;
    businessUnitAttempted;
    subBusinessUnitAttempted;
    kpiMasterAttempted;
    industryAttempted;
    subIndustryAttempted;
}
exports.UnprocessedKpiPerticularRecordDto = UnprocessedKpiPerticularRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedKpiPerticularRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiPerticularRecordDto.prototype, "accountAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Business unit identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiPerticularRecordDto.prototype, "businessUnitAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Sub business unit identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiPerticularRecordDto.prototype, "subBusinessUnitAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KPI master identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiPerticularRecordDto.prototype, "kpiMasterAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Industry identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiPerticularRecordDto.prototype, "industryAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Sub-industry identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiPerticularRecordDto.prototype, "subIndustryAttempted", void 0);
class KpiPerticularProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.KpiPerticularProcessedDataDto = KpiPerticularProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], KpiPerticularProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings.',
        type: () => [UnprocessedKpiPerticularRecordDto],
    }),
    __metadata("design:type", Array)
], KpiPerticularProcessedDataDto.prototype, "unprocessedRecords", void 0);
class KpiPerticularApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.KpiPerticularApiResponseDto = KpiPerticularApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: KpiPerticularProcessedDataDto }),
    __metadata("design:type", KpiPerticularProcessedDataDto)
], KpiPerticularApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=kpi-perticular-response.dto.js.map