import { Repository } from 'typeorm';
import { JvsKpiMaster } from './jvs-kpi-master.entity';
import { KpiMasterRecord } from './dto/upsert-kpi-master.dto';
interface UpsertKpiMasterResult {
    processed: number;
    unprocessedRecords: never[];
}
export declare class KpiMasterService {
    private readonly kpiMasterRepository;
    constructor(kpiMasterRepository: Repository<JvsKpiMaster>);
    upsertKpiMasters(records: KpiMasterRecord[]): Promise<UpsertKpiMasterResult>;
}
export {};
