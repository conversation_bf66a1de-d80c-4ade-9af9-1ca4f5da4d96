import { VectorMasterService } from './vector-master.service';
import { UpsertVectorMasterDto } from './dto/upsert-vector-master.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class VectorMasterController {
    private readonly vectorMasterService;
    private static readonly validationPipe;
    constructor(vectorMasterService: VectorMasterService);
    upsertVectorMasters(payload: UpsertVectorMasterDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: never[];
    }>>;
}
