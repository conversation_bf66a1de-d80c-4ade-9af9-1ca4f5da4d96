"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const data_source_1 = __importDefault(require("../../data-source"));
async function revertAllMigrations() {
    await data_source_1.default.initialize();
    try {
        const migrationsTableName = data_source_1.default.options.migrationsTableName ?? 'migrations';
        const schemaName = data_source_1.default.options.schema;
        const tablePath = data_source_1.default.driver
            .buildTableName(migrationsTableName, schemaName)
            .split('.')
            .map((part) => data_source_1.default.driver.escape(part))
            .join('.');
        while (true) {
            const executedMigrations = await data_source_1.default.query(`SELECT "id", "name", "timestamp" FROM ${tablePath} ORDER BY "id" DESC`);
            if (executedMigrations.length === 0) {
                console.log('No executed migrations found. Database is already clean.');
                break;
            }
            const [lastMigration] = executedMigrations;
            console.log(`Reverting migration ${lastMigration.name} (${lastMigration.timestamp})`);
            await data_source_1.default.undoLastMigration();
        }
    }
    catch (error) {
        console.error('Failed to revert migrations:', error);
        process.exitCode = 1;
    }
    finally {
        await data_source_1.default.destroy();
    }
}
void revertAllMigrations();
//# sourceMappingURL=revert-all-migrations.js.map