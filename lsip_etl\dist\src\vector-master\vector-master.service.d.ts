import { Repository } from 'typeorm';
import { JvsVectorMaster } from './jvs-vector-master.entity';
import { VectorMasterRecord } from './dto/upsert-vector-master.dto';
interface UpsertVectorMasterResult {
    processed: number;
    unprocessedRecords: never[];
}
export declare class VectorMasterService {
    private readonly vectorMasterRepository;
    constructor(vectorMasterRepository: Repository<JvsVectorMaster>);
    upsertVectorMasters(records: VectorMasterRecord[]): Promise<UpsertVectorMasterResult>;
}
export {};
