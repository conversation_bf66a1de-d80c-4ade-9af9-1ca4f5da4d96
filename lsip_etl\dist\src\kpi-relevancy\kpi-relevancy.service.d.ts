import { Repository } from 'typeorm';
import { JvsKpiRelevancy } from './jvs-kpi-relevancy.entity';
import { KpiRelevancyRecord } from './dto/upsert-kpi-relevancy.dto';
import { JvsAccount } from '../accounts/jvs-account.entity';
import { JvsKpiMaster } from '../kpi-master/jvs-kpi-master.entity';
import { JvsIndustry } from '../industries/jvs-industry.entity';
interface UnprocessedKpiRelevancyRecord extends KpiRelevancyRecord {
    message: string;
    companyAttempted: string | null;
    industryMasterAttempted: string | null;
    kpiMasterAttempted: string | null;
    subIndustryAttempted: string | null;
}
interface UpsertKpiRelevancyResult {
    processed: number;
    unprocessedRecords: UnprocessedKpiRelevancyRecord[];
}
export declare class KpiRelevancyService {
    private readonly kpiRelevancyRepository;
    private readonly accountRepository;
    private readonly kpiMasterRepository;
    private readonly industryRepository;
    constructor(kpiRelevancyRepository: Repository<JvsKpiRelevancy>, accountRepository: Repository<JvsAccount>, kpiMasterRepository: Repository<JvsKpiMaster>, industryRepository: Repository<JvsIndustry>);
    upsertKpiRelevancies(records: KpiRelevancyRecord[]): Promise<UpsertKpiRelevancyResult>;
}
export {};
