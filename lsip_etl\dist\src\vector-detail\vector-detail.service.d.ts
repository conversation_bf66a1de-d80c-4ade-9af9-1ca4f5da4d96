import { Repository } from 'typeorm';
import { JvsVectorDetail } from './jvs-vector-detail.entity';
import { VectorDetailRecord } from './dto/upsert-vector-detail.dto';
import { JvsKpiPerticular } from '../kpi-perticular/jvs-kpi-perticular.entity';
import { JvsVectorMaster } from '../vector-master/jvs-vector-master.entity';
interface UnprocessedVectorDetailRecord extends VectorDetailRecord {
    message: string;
    kpiPerticularAttempted: string | null;
    vectorMasterAttempted: string | null;
}
interface UpsertVectorDetailResult {
    processed: number;
    unprocessedRecords: UnprocessedVectorDetailRecord[];
}
export declare class VectorDetailService {
    private readonly vectorDetailRepository;
    private readonly kpiPerticularRepository;
    private readonly vectorMasterRepository;
    constructor(vectorDetailRepository: Repository<JvsVectorDetail>, kpiPerticularRepository: Repository<JvsKpiPerticular>, vectorMasterRepository: Repository<JvsVectorMaster>);
    upsertVectorDetails(records: VectorDetailRecord[]): Promise<UpsertVectorDetailResult>;
}
export {};
