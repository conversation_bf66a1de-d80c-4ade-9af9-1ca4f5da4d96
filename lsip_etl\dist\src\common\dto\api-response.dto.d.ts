export type ApiStatus = 'success' | 'error';
export interface ApiResponse<T> {
    status: ApiStatus;
    message: string;
    data: T;
    error?: string | null;
}
export declare class BaseApiResponseDto {
    status: ApiStatus;
    message: string;
    error: string | null;
    data: unknown;
}
export declare class StringResponseDto extends BaseApiResponseDto {
    data: string;
}
export declare const createSuccessResponse: <T>(message: string, data: T) => ApiResponse<T>;
export declare const createErrorResponse: <T>(message: string, error: string, data?: T | null) => ApiResponse<T | null>;
