"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsFounderRating = void 0;
const typeorm_1 = require("typeorm");
let JvsFounderRating = class JvsFounderRating {
    id;
    name = null;
    createdDate = null;
    accountId = null;
    visionStrategicThinking = null;
    adaptabilityLearnability = null;
    goalOrientation = null;
    orgBuildingAbilityToAttractHighQ = null;
    narrativeBuildingStoryTelling = null;
    financialPrudenceBurnManagement = null;
    paceOfExecutionBiasToAction = null;
    abilityToTakeToughDecisions = null;
    leadingOthersOutcomeFromTeam = null;
    selfAwareness = null;
    integrityTransparency = null;
    ratingDate = null;
};
exports.JvsFounderRating = JvsFounderRating;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsFounderRating.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'createddate', type: 'timestamptz', nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "createdDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'account__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "accountId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vision_strategic_thinking__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "visionStrategicThinking", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'adaptability_learnability__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "adaptabilityLearnability", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'goal_orientation__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "goalOrientation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'org_building_ability_to_attract_high_q__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "orgBuildingAbilityToAttractHighQ", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'narrative_building_story_telling__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "narrativeBuildingStoryTelling", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'financial_prudence_burn_management__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "financialPrudenceBurnManagement", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'pace_of_execution_bias_to_action__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "paceOfExecutionBiasToAction", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ability_to_take_tough_decisions__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "abilityToTakeToughDecisions", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'leading_others_outcome_from_team__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "leadingOthersOutcomeFromTeam", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'self_awareness__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "selfAwareness", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'integrity_transparency__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "integrityTransparency", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'rating_date__c', type: 'timestamptz', nullable: true }),
    __metadata("design:type", Object)
], JvsFounderRating.prototype, "ratingDate", void 0);
exports.JvsFounderRating = JvsFounderRating = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_founder_ratings' })
], JvsFounderRating);
//# sourceMappingURL=jvs-founder-rating.entity.js.map