import { KpiPlanActualService } from './kpi-plan-actual.service';
import { UpsertKpiPlanActualDto } from './dto/upsert-kpi-plan-actual.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class KpiPlanActualController {
    private readonly kpiPlanActualService;
    private static readonly validationPipe;
    constructor(kpiPlanActualService: KpiPlanActualService);
    upsertKpiPlanActuals(payload: UpsertKpiPlanActualDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            kpiPerticularAttempted: string | null;
        }[];
    }>>;
}
