"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorPlanActualModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const vector_plan_actual_controller_1 = require("./vector-plan-actual.controller");
const vector_plan_actual_service_1 = require("./vector-plan-actual.service");
const jvs_vector_plan_actual_entity_1 = require("./jvs-vector-plan-actual.entity");
const jvs_vector_detail_entity_1 = require("../vector-detail/jvs-vector-detail.entity");
const jvs_kpi_plan_actual_entity_1 = require("../kpi-plan-actual/jvs-kpi-plan-actual.entity");
let VectorPlanActualModule = class VectorPlanActualModule {
};
exports.VectorPlanActualModule = VectorPlanActualModule;
exports.VectorPlanActualModule = VectorPlanActualModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_vector_plan_actual_entity_1.JvsVectorPlanActual, jvs_vector_detail_entity_1.JvsVectorDetail, jvs_kpi_plan_actual_entity_1.JvsKpiPlanActual])],
        controllers: [vector_plan_actual_controller_1.VectorPlanActualController],
        providers: [vector_plan_actual_service_1.VectorPlanActualService],
        exports: [vector_plan_actual_service_1.VectorPlanActualService],
    })
], VectorPlanActualModule);
//# sourceMappingURL=vector-plan-actual.module.js.map