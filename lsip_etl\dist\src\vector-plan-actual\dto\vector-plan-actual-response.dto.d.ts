import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { VectorPlanActualRecordDto } from './upsert-vector-plan-actual.dto';
export declare class UnprocessedVectorPlanActualRecordDto extends VectorPlanActualRecordDto {
    message: string;
    vectorDetailAttempted: string | null;
    kpiPlanActualAttempted: string | null;
}
export declare class VectorPlanActualProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedVectorPlanActualRecordDto[];
}
export declare class VectorPlanActualApiResponseDto extends BaseApiResponseDto {
    data: VectorPlanActualProcessedDataDto;
}
