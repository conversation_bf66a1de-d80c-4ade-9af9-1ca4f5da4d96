export declare class BusinessRatingRecordDto {
    id: string;
    name?: string;
    createddate?: string;
    account__c?: string;
    product_vision_strategy__c?: string;
    product_vision_strategy_des__c?: string;
    gtm_muscle_ent_sales_partnership_des__c?: string;
    gtm_muscle_ent_sales_partnership__c?: string;
    data_technology_orientation__c?: string;
    data_technology_orientation_des__c?: string;
    deep_tech_engg_product_leadership__c?: string;
    deep_tech_engg_product_leadership_des__c?: string;
    gtm_muscle_commercialization__c?: string;
    gtm_muscle_commercialization_des__c?: string;
    regulatory__c?: string;
    regulatory_des__c?: string;
    product_vision_strategy_ai_ml__c?: string;
    product_vision_strategy_ai_ml_des__c?: string;
    gtm_muscle_for_devtools_prosumer__c?: string;
    gtm_muscle_for_devtools_prosumer_des__c?: string;
    narrative_building__c?: string;
    narrative_building_desc__c?: string;
    product_vision_strategy_ai_llm__c?: string;
    product_vision_strategy_ai_llm_desc__c?: string;
    gtm_muscle__c?: string;
    gtm_muscle_desc__c?: string;
    positioning_narrative_building__c?: string;
    positioning_narrative_building_desc__c?: string;
    product_strategy_strategic_thinking__c?: string;
    product_strategy_strategic_thinking_des__c?: string;
    marketing_narrative_building__c?: string;
    marketing_narrative_building_desc__c?: string;
    ai_capability__c?: string;
    ai_capability_desc__c?: string;
    category_creation__c?: string;
    category_creation_desc__c?: string;
}
export declare class UpsertBusinessRatingsDto {
    BusinessRatings: BusinessRatingRecordDto[];
}
export type BusinessRatingRecord = BusinessRatingRecordDto;
