"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertVectorMasterDto = exports.VectorMasterRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class VectorMasterRecordDto {
    id;
    name;
    currencyIsoCode;
}
exports.VectorMasterRecordDto = VectorMasterRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the vector master record.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], VectorMasterRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Vector master name.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], VectorMasterRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Currency ISO code.', maxLength: 3 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(3),
    __metadata("design:type", String)
], VectorMasterRecordDto.prototype, "currencyIsoCode", void 0);
class UpsertVectorMasterDto {
    VectorMasters;
}
exports.UpsertVectorMasterDto = UpsertVectorMasterDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [VectorMasterRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => VectorMasterRecordDto),
    __metadata("design:type", Array)
], UpsertVectorMasterDto.prototype, "VectorMasters", void 0);
//# sourceMappingURL=upsert-vector-master.dto.js.map