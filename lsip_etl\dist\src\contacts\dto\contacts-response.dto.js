"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsApiResponseDto = exports.ContactsProcessedDataDto = exports.UnprocessedContactRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_contacts_dto_1 = require("./upsert-contacts.dto");
class UnprocessedContactRecordDto extends upsert_contacts_dto_1.ContactRecordDto {
    message;
    accountAttempted;
    companyFundAttempted;
    reportsToAttempted;
}
exports.UnprocessedContactRecordDto = UnprocessedContactRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedContactRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedContactRecordDto.prototype, "accountAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Company fund identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedContactRecordDto.prototype, "companyFundAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reports-to contact identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedContactRecordDto.prototype, "reportsToAttempted", void 0);
class ContactsProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.ContactsProcessedDataDto = ContactsProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], ContactsProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings (e.g., unresolved references).',
        type: () => [UnprocessedContactRecordDto],
    }),
    __metadata("design:type", Array)
], ContactsProcessedDataDto.prototype, "unprocessedRecords", void 0);
class ContactsApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.ContactsApiResponseDto = ContactsApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: ContactsProcessedDataDto }),
    __metadata("design:type", ContactsProcessedDataDto)
], ContactsApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=contacts-response.dto.js.map