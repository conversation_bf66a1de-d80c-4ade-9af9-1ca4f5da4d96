"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiPerticularService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_kpi_perticular_entity_1 = require("./jvs-kpi-perticular.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
const jvs_business_unit_entity_1 = require("../business-units/jvs-business-unit.entity");
const jvs_kpi_master_entity_1 = require("../kpi-master/jvs-kpi-master.entity");
const jvs_industry_entity_1 = require("../industries/jvs-industry.entity");
let KpiPerticularService = class KpiPerticularService {
    kpiPerticularRepository;
    accountRepository;
    businessUnitRepository;
    kpiMasterRepository;
    industryRepository;
    constructor(kpiPerticularRepository, accountRepository, businessUnitRepository, kpiMasterRepository, industryRepository) {
        this.kpiPerticularRepository = kpiPerticularRepository;
        this.accountRepository = accountRepository;
        this.businessUnitRepository = businessUnitRepository;
        this.kpiMasterRepository = kpiMasterRepository;
        this.industryRepository = industryRepository;
    }
    async upsertKpiPerticulars(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedAccountIds = new Set(records.map((record) => record.company__c).filter((value) => Boolean(value)));
        const requestedBusinessUnitIds = new Set(records
            .flatMap((record) => [record.business_unit__c, record.sub_business_unit__c])
            .filter((value) => Boolean(value)));
        const requestedKpiMasterIds = new Set(records.map((record) => record.kpi_master__c).filter((value) => Boolean(value)));
        const requestedIndustryIds = new Set(records
            .flatMap((record) => [record.industry__c, record.sub_industry__c])
            .filter((value) => Boolean(value)));
        const existingAccountIds = requestedAccountIds.size
            ? new Set((await this.accountRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedAccountIds]) },
            })).map((account) => account.id))
            : new Set();
        const existingBusinessUnitIds = requestedBusinessUnitIds.size
            ? new Set((await this.businessUnitRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedBusinessUnitIds]) },
            })).map((unit) => unit.id))
            : new Set();
        const existingKpiMasterIds = requestedKpiMasterIds.size
            ? new Set((await this.kpiMasterRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedKpiMasterIds]) },
            })).map((kpiMaster) => kpiMaster.id))
            : new Set();
        const existingIndustryIds = requestedIndustryIds.size
            ? new Set((await this.industryRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedIndustryIds]) },
            })).map((industry) => industry.id))
            : new Set();
        const entities = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                accountAttempted: null,
                businessUnitAttempted: null,
                subBusinessUnitAttempted: null,
                kpiMasterAttempted: null,
                industryAttempted: null,
                subIndustryAttempted: null,
            };
            const companyId = record.company__c && existingAccountIds.has(record.company__c) ? record.company__c : null;
            if (record.company__c && !companyId) {
                meta.accountAttempted = record.company__c;
                meta.issues.push(`Account ${record.company__c} not found; stored without company linkage.`);
            }
            const businessUnitId = record.business_unit__c && existingBusinessUnitIds.has(record.business_unit__c)
                ? record.business_unit__c
                : null;
            if (record.business_unit__c && !businessUnitId) {
                meta.businessUnitAttempted = record.business_unit__c;
                meta.issues.push(`Business unit ${record.business_unit__c} not found; stored without business unit linkage.`);
            }
            const subBusinessUnitId = record.sub_business_unit__c && existingBusinessUnitIds.has(record.sub_business_unit__c)
                ? record.sub_business_unit__c
                : null;
            if (record.sub_business_unit__c && !subBusinessUnitId) {
                meta.subBusinessUnitAttempted = record.sub_business_unit__c;
                meta.issues.push(`Sub business unit ${record.sub_business_unit__c} not found; stored without sub business unit linkage.`);
            }
            const kpiMasterId = record.kpi_master__c && existingKpiMasterIds.has(record.kpi_master__c) ? record.kpi_master__c : null;
            if (record.kpi_master__c && !kpiMasterId) {
                meta.kpiMasterAttempted = record.kpi_master__c;
                meta.issues.push(`KPI master ${record.kpi_master__c} not found; stored without KPI master linkage.`);
            }
            const industryId = record.industry__c && existingIndustryIds.has(record.industry__c) ? record.industry__c : null;
            if (record.industry__c && !industryId) {
                meta.industryAttempted = record.industry__c;
                meta.issues.push(`Industry ${record.industry__c} not found; stored without industry linkage.`);
            }
            const subIndustryId = record.sub_industry__c && existingIndustryIds.has(record.sub_industry__c)
                ? record.sub_industry__c
                : null;
            if (record.sub_industry__c && !subIndustryId) {
                meta.subIndustryAttempted = record.sub_industry__c;
                meta.issues.push(`Sub-industry ${record.sub_industry__c} not found; stored without sub-industry linkage.`);
            }
            const entity = this.kpiPerticularRepository.create({
                id: record.id,
                name: record.name ?? null,
                currencyIsoCode: record.currencyisocode ?? null,
                businessUnitId,
                companyId,
                creationType: record.creation_type__c ?? null,
                formulaId: record.formulaid__c ?? null,
                formula: record.formula__c ?? null,
                kpiMasterId,
                kpiParticularLevelNumber: record.kpi_particular_level_number__c ?? null,
                kpiParticularLevel: record.kpi_particular_level__c ?? null,
                nameSet: record.name_set__c ?? null,
                alternateName: record.name__c ?? null,
                planRequired: record.plan_required__c ?? null,
                subBusinessUnitId,
                uniqueIdentifier: record.unique_identifier__c ?? null,
                benchmarkId: record.benchmark__c ?? null,
                industryId,
                kpiParticularType: record.kpi_particular_type__c ?? null,
                subIndustryId,
                entityId: record.entity_id__c ?? null,
            });
            metaById.set(record.id, meta);
            entities.push(entity);
        }
        if (entities.length > 0) {
            await this.kpiPerticularRepository.save(entities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    accountAttempted: meta.accountAttempted,
                    businessUnitAttempted: meta.businessUnitAttempted,
                    subBusinessUnitAttempted: meta.subBusinessUnitAttempted,
                    kpiMasterAttempted: meta.kpiMasterAttempted,
                    industryAttempted: meta.industryAttempted,
                    subIndustryAttempted: meta.subIndustryAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.KpiPerticularService = KpiPerticularService;
exports.KpiPerticularService = KpiPerticularService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_kpi_perticular_entity_1.JvsKpiPerticular)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __param(2, (0, typeorm_1.InjectRepository)(jvs_business_unit_entity_1.JvsBusinessUnit)),
    __param(3, (0, typeorm_1.InjectRepository)(jvs_kpi_master_entity_1.JvsKpiMaster)),
    __param(4, (0, typeorm_1.InjectRepository)(jvs_industry_entity_1.JvsIndustry)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], KpiPerticularService);
//# sourceMappingURL=kpi-perticular.service.js.map