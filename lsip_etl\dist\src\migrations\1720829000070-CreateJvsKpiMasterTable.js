"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJvsKpiMasterTable1720829000070 = void 0;
class CreateJvsKpiMasterTable1720829000070 {
    name = 'CreateJvsKpiMasterTable1720829000070';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_kpi_master (
              id character varying(18) NOT NULL,
              name character varying(80),
              currencyisocode character varying(3),
              category__c character varying(255),
              type__c character varying(255),
              unit__c character varying(255),
              CONSTRAINT pk_jvs_kpi_master_id PRIMARY KEY (id)
            );
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_master_category__c ON jvs_kpi_master (category__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_master_type__c ON jvs_kpi_master (type__c);
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_master_type__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_master_category__c;
    `);
        await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_kpi_master;
    `);
    }
}
exports.CreateJvsKpiMasterTable1720829000070 = CreateJvsKpiMasterTable1720829000070;
//# sourceMappingURL=1720829000070-CreateJvsKpiMasterTable.js.map