import { VectorPlanActualService } from './vector-plan-actual.service';
import { UpsertVectorPlanActualDto } from './dto/upsert-vector-plan-actual.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class VectorPlanActualController {
    private readonly vectorPlanActualService;
    private static readonly validationPipe;
    constructor(vectorPlanActualService: VectorPlanActualService);
    upsertVectorPlanActuals(payload: UpsertVectorPlanActualDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            vectorDetailAttempted: string | null;
            kpiPlanActualAttempted: string | null;
        }[];
    }>>;
}
