"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertKpiPlanActualDto = exports.KpiPlanActualRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class KpiPlanActualRecordDto {
    id;
    name;
    actual__c;
    kpi_particular__c;
    month1__c;
    name__c;
    number_of_plan_revisions__c;
    plan__c;
    quarter__c;
    reason_for_deviation_remarks__c;
    year__c;
    actual_formula__c;
    month_number__c;
    percentage_of_achievement__c;
    consolidated_date__c;
    plan_actual_date__c;
}
exports.KpiPlanActualRecordDto = KpiPlanActualRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the KPI plan actual record.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Display name.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Actual value.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "actual__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'KPI particular identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "kpi_particular__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Month indicator string.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "month1__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Alternate name.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "name__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of plan revisions.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiPlanActualRecordDto.prototype, "number_of_plan_revisions__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Plan value.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "plan__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Quarter label.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "quarter__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Reason for deviation remarks.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "reason_for_deviation_remarks__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Fiscal year.', maxLength: 4 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "year__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Actual formula.', maxLength: 1300 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1300),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "actual_formula__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Month number for the KPI actual.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiPlanActualRecordDto.prototype, "month_number__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Percentage of achievement.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiPlanActualRecordDto.prototype, "percentage_of_achievement__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Consolidated date text.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "consolidated_date__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Plan/actual date.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], KpiPlanActualRecordDto.prototype, "plan_actual_date__c", void 0);
class UpsertKpiPlanActualDto {
    KpiPlanActuals;
}
exports.UpsertKpiPlanActualDto = UpsertKpiPlanActualDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [KpiPlanActualRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => KpiPlanActualRecordDto),
    __metadata("design:type", Array)
], UpsertKpiPlanActualDto.prototype, "KpiPlanActuals", void 0);
//# sourceMappingURL=upsert-kpi-plan-actual.dto.js.map