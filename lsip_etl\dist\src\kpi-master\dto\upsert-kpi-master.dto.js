"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertKpiMasterDto = exports.KpiMasterRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class KpiMasterRecordDto {
    id;
    name;
    currencyisocode;
    category__c;
    type__c;
    unit__c;
}
exports.KpiMasterRecordDto = KpiMasterRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the KPI master record.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiMasterRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the KPI master entry.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], KpiMasterRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Currency ISO code.', maxLength: 3 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 3),
    __metadata("design:type", String)
], KpiMasterRecordDto.prototype, "currencyisocode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'KPI category.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiMasterRecordDto.prototype, "category__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'KPI type.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiMasterRecordDto.prototype, "type__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Measurement unit.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiMasterRecordDto.prototype, "unit__c", void 0);
class UpsertKpiMasterDto {
    KpiMasters;
}
exports.UpsertKpiMasterDto = UpsertKpiMasterDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [KpiMasterRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => KpiMasterRecordDto),
    __metadata("design:type", Array)
], UpsertKpiMasterDto.prototype, "KpiMasters", void 0);
//# sourceMappingURL=upsert-kpi-master.dto.js.map