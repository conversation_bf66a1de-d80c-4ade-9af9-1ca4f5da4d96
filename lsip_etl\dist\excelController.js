"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const googleapis_1 = require("googleapis");
const XLSX = __importStar(require("xlsx"));
const KEYFILEPATH = path.join(__dirname, "..", "internship-poc-473709-2650dce5e12a.json");
const SCOPES = ["https://www.googleapis.com/auth/drive.readonly"];
const auth = new googleapis_1.google.auth.GoogleAuth({
    keyFile: KEYFILEPATH,
    scopes: SCOPES,
});
const drive = googleapis_1.google.drive({ version: "v3", auth });
const FILE_ID = "1krtI31BYIf3f5Ei2ATlInsgZjuQIAUiC";
async function downloadExcel() {
    try {
        const response = await drive.files.get({ fileId: FILE_ID, alt: "media" }, { responseType: "arraybuffer" });
        const buffer = Buffer.from(response.data);
        const workbook = XLSX.read(buffer, { type: "buffer" });
        console.log('Size of Sheets : ', workbook.SheetNames);
        // Create the output directory if it doesn't exist
        const outputDir = path.join(__dirname, "ExcelJsonData");
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        for (let i = 0; i < workbook.SheetNames.length; i++) {
            if (workbook.SheetNames[i] === "Cap table") {
                const sheetName = workbook.SheetNames[i];
                const sheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(sheet);
                const jsonString = JSON.stringify(jsonData, null, 2);
                const fileName = workbook.SheetNames[i].trim();
                const filePath = path.join(outputDir, `${fileName}.json`);
                fs.writeFileSync(filePath, jsonString, "utf-8");
                console.log(`Saved: ${filePath}`);
            }
        }
        console.log("JSON data has been saved");
    }
    catch (error) {
        console.error("Error downloading Excel file:", error);
        throw error;
    }
}
downloadExcel();
