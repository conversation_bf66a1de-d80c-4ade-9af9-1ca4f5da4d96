"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecordsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
const jvs_business_rating_entity_1 = require("../business-ratings/jvs-business-rating.entity");
const jvs_business_unit_entity_1 = require("../business-units/jvs-business-unit.entity");
const jvs_company_fund_entity_1 = require("../company-funds/jvs-company-fund.entity");
const jvs_contact_entity_1 = require("../contacts/jvs-contact.entity");
const jvs_founder_rating_entity_1 = require("../founder-ratings/jvs-founder-rating.entity");
const jvs_industry_entity_1 = require("../industries/jvs-industry.entity");
const jvs_kpi_detail_entity_1 = require("../kpi-details/jvs-kpi-detail.entity");
const jvs_kpi_master_entity_1 = require("../kpi-master/jvs-kpi-master.entity");
const jvs_kpi_perticular_entity_1 = require("../kpi-perticular/jvs-kpi-perticular.entity");
const jvs_kpi_plan_actual_entity_1 = require("../kpi-plan-actual/jvs-kpi-plan-actual.entity");
const jvs_kpi_relevancy_entity_1 = require("../kpi-relevancy/jvs-kpi-relevancy.entity");
const jvs_vector_detail_entity_1 = require("../vector-detail/jvs-vector-detail.entity");
const jvs_vector_master_entity_1 = require("../vector-master/jvs-vector-master.entity");
const jvs_vector_plan_actual_entity_1 = require("../vector-plan-actual/jvs-vector-plan-actual.entity");
const records_controller_1 = require("./records.controller");
const records_service_1 = require("./records.service");
let RecordsModule = class RecordsModule {
};
exports.RecordsModule = RecordsModule;
exports.RecordsModule = RecordsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                jvs_account_entity_1.JvsAccount,
                jvs_business_rating_entity_1.JvsBusinessRating,
                jvs_business_unit_entity_1.JvsBusinessUnit,
                jvs_company_fund_entity_1.JvsCompanyFund,
                jvs_contact_entity_1.JvsContact,
                jvs_founder_rating_entity_1.JvsFounderRating,
                jvs_industry_entity_1.JvsIndustry,
                jvs_kpi_detail_entity_1.JvsKpiDetail,
                jvs_kpi_master_entity_1.JvsKpiMaster,
                jvs_kpi_perticular_entity_1.JvsKpiPerticular,
                jvs_kpi_plan_actual_entity_1.JvsKpiPlanActual,
                jvs_kpi_relevancy_entity_1.JvsKpiRelevancy,
                jvs_vector_master_entity_1.JvsVectorMaster,
                jvs_vector_detail_entity_1.JvsVectorDetail,
                jvs_vector_plan_actual_entity_1.JvsVectorPlanActual,
            ]),
        ],
        controllers: [records_controller_1.RecordsController],
        providers: [records_service_1.RecordsService],
    })
], RecordsModule);
//# sourceMappingURL=records.module.js.map