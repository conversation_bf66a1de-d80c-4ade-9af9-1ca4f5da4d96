// import * as fs from "fs";
// import * as path from "path";
// import { drive_v3, google } from "googleapis";
// import * as XLSX from "xlsx";
// import { WorkBook, WorkSheet } from "xlsx";

// const KEYFILEPATH: string = path.join(__dirname, "..", "internship-poc-473709-2650dce5e12a.json");
// const SCOPES: string[] = ["https://www.googleapis.com/auth/drive.readonly"];

// const auth = new google.auth.GoogleAuth({
//   keyFile: KEYFILEPATH,
//   scopes: SCOPES,
// });

// const drive: drive_v3.Drive = google.drive({ version: "v3", auth });

// const FILE_ID: string = "1krtI31BYIf3f5Ei2ATlInsgZjuQIAUiC";

// async function downloadExcel() {
//   try {
//     const response = await drive.files.get(
//       { fileId: FILE_ID, alt: "media" },
//       { responseType: "arraybuffer" }
//     );

//     const buffer: Buffer = Buffer.from(response.data as ArrayBuffer);
//     const workbook: WorkBook = XLSX.read(buffer, { type: "buffer" });

//     console.log('Size of Sheets : ', workbook.SheetNames);

//     // Create the output directory if it doesn't exist
//     const outputDir = path.join(__dirname, "ExcelJsonData");
//     if (!fs.existsSync(outputDir)) {
//       fs.mkdirSync(outputDir, { recursive: true });
//     }

//     for (let i = 0; i < workbook.SheetNames.length; i++) {
//       if(workbook.SheetNames[i] === "Cap table" ) {
//         const sheetName: string = workbook.SheetNames[i];
//         const sheet: WorkSheet = workbook.Sheets[sheetName];

//         const jsonData: any = XLSX.utils.sheet_to_json(sheet);
//         const jsonString: string = JSON.stringify(jsonData, null, 2);

//         const fileName: string = workbook.SheetNames[i].trim();
//         const filePath = path.join(outputDir, `${fileName}.json`);
//         fs.writeFileSync(filePath, jsonString, "utf-8");
//         console.log(`Saved: ${filePath}`);
//       }
//     }

//     console.log("JSON data has been saved");
//   } catch (error) {
//     console.error("Error downloading Excel file:", error);
//     throw error;
//   }
// }

// downloadExcel();