"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FounderRatingsApiResponseDto = exports.FounderRatingsProcessedDataDto = exports.UnprocessedFounderRatingRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_founder_ratings_dto_1 = require("./upsert-founder-ratings.dto");
class UnprocessedFounderRatingRecordDto extends upsert_founder_ratings_dto_1.FounderRatingRecordDto {
    message;
    accountAttempted;
}
exports.UnprocessedFounderRatingRecordDto = UnprocessedFounderRatingRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedFounderRatingRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedFounderRatingRecordDto.prototype, "accountAttempted", void 0);
class FounderRatingsProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.FounderRatingsProcessedDataDto = FounderRatingsProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], FounderRatingsProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings.',
        type: () => [UnprocessedFounderRatingRecordDto],
    }),
    __metadata("design:type", Array)
], FounderRatingsProcessedDataDto.prototype, "unprocessedRecords", void 0);
class FounderRatingsApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.FounderRatingsApiResponseDto = FounderRatingsApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: FounderRatingsProcessedDataDto }),
    __metadata("design:type", FounderRatingsProcessedDataDto)
], FounderRatingsApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=founder-ratings-response.dto.js.map