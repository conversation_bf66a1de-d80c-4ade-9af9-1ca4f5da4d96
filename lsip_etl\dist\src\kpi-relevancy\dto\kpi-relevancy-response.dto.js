"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiRelevancyApiResponseDto = exports.KpiRelevancyProcessedDataDto = exports.UnprocessedKpiRelevancyRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_kpi_relevancy_dto_1 = require("./upsert-kpi-relevancy.dto");
class UnprocessedKpiRelevancyRecordDto extends upsert_kpi_relevancy_dto_1.KpiRelevancyRecordDto {
    message;
    companyAttempted;
    industryMasterAttempted;
    kpiMasterAttempted;
    subIndustryAttempted;
}
exports.UnprocessedKpiRelevancyRecordDto = UnprocessedKpiRelevancyRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedKpiRelevancyRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiRelevancyRecordDto.prototype, "companyAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Industry master identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiRelevancyRecordDto.prototype, "industryMasterAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KPI master identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiRelevancyRecordDto.prototype, "kpiMasterAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Sub-industry identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiRelevancyRecordDto.prototype, "subIndustryAttempted", void 0);
class KpiRelevancyProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.KpiRelevancyProcessedDataDto = KpiRelevancyProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], KpiRelevancyProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings.',
        type: () => [UnprocessedKpiRelevancyRecordDto],
    }),
    __metadata("design:type", Array)
], KpiRelevancyProcessedDataDto.prototype, "unprocessedRecords", void 0);
class KpiRelevancyApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.KpiRelevancyApiResponseDto = KpiRelevancyApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: KpiRelevancyProcessedDataDto }),
    __metadata("design:type", KpiRelevancyProcessedDataDto)
], KpiRelevancyApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=kpi-relevancy-response.dto.js.map