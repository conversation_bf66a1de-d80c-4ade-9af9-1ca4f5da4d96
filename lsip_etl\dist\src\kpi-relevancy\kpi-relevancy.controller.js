"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiRelevancyController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const kpi_relevancy_service_1 = require("./kpi-relevancy.service");
const upsert_kpi_relevancy_dto_1 = require("./dto/upsert-kpi-relevancy.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const kpi_relevancy_response_dto_1 = require("./dto/kpi-relevancy-response.dto");
let KpiRelevancyController = class KpiRelevancyController {
    kpiRelevancyService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(kpiRelevancyService) {
        this.kpiRelevancyService = kpiRelevancyService;
    }
    async upsertKpiRelevancies(payload) {
        const result = await this.kpiRelevancyService.upsertKpiRelevancies(payload.KpiRelevancies);
        return (0, api_response_dto_1.createSuccessResponse)('KPI relevancy records processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.KpiRelevancyController = KpiRelevancyController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(KpiRelevancyController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert KPI relevancy records.' }),
    (0, swagger_1.ApiBody)({ type: upsert_kpi_relevancy_dto_1.UpsertKpiRelevancyDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'KPI relevancy records processed successfully.',
        type: kpi_relevancy_response_dto_1.KpiRelevancyApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_kpi_relevancy_dto_1.UpsertKpiRelevancyDto]),
    __metadata("design:returntype", Promise)
], KpiRelevancyController.prototype, "upsertKpiRelevancies", null);
exports.KpiRelevancyController = KpiRelevancyController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('kpi-relevancies'),
    __metadata("design:paramtypes", [kpi_relevancy_service_1.KpiRelevancyService])
], KpiRelevancyController);
//# sourceMappingURL=kpi-relevancy.controller.js.map