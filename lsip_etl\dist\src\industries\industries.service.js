"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndustriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_industry_entity_1 = require("./jvs-industry.entity");
let IndustriesService = class IndustriesService {
    repository;
    constructor(repository) {
        this.repository = repository;
    }
    async upsertIndustries(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const topLevelEntities = [];
        const childCandidates = [];
        for (const record of records) {
            const parentId = record.parent_industry__c ?? null;
            const entity = this.repository.create({
                id: record.id,
                name: record.name ?? null,
                frequency: record.frequency__c ?? null,
                parentIndustryId: null,
            });
            if (parentId) {
                childCandidates.push({ entity, parentId, original: record });
            }
            else {
                topLevelEntities.push(entity);
            }
        }
        if (topLevelEntities.length > 0) {
            await this.repository.save(topLevelEntities);
        }
        const requestedParentIds = Array.from(new Set(childCandidates.map((candidate) => candidate.parentId)));
        const existingParents = requestedParentIds.length
            ? await this.repository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedParentIds) },
            })
            : [];
        const resolvedParentIds = new Set([
            ...existingParents.map((industry) => industry.id),
            ...topLevelEntities.map((industry) => industry.id),
        ]);
        const unprocessedRecords = [];
        const childEntities = [];
        let pendingCandidates = [...childCandidates];
        while (pendingCandidates.length > 0) {
            const deferredCandidates = [];
            let processedInCurrentPass = false;
            for (const candidate of pendingCandidates) {
                if (resolvedParentIds.has(candidate.parentId)) {
                    candidate.entity.parentIndustryId = candidate.parentId;
                    resolvedParentIds.add(candidate.entity.id);
                    childEntities.push(candidate.entity);
                    processedInCurrentPass = true;
                }
                else {
                    deferredCandidates.push(candidate);
                }
            }
            if (!processedInCurrentPass) {
                for (const candidate of deferredCandidates) {
                    candidate.entity.parentIndustryId = null;
                    childEntities.push(candidate.entity);
                    unprocessedRecords.push({
                        id: candidate.entity.id,
                        name: candidate.entity.name,
                        frequency__c: candidate.original.frequency__c,
                        parent_industry__c: candidate.original.parent_industry__c,
                        parentIndustryAttempted: candidate.parentId,
                        message: `Parent industry ${candidate.parentId} not found; stored without parent reference.`,
                    });
                }
                break;
            }
            pendingCandidates = deferredCandidates;
        }
        if (childEntities.length > 0) {
            await this.repository.save(childEntities);
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.IndustriesService = IndustriesService;
exports.IndustriesService = IndustriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_industry_entity_1.JvsIndustry)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], IndustriesService);
//# sourceMappingURL=industries.service.js.map