"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorDetailController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const vector_detail_service_1 = require("./vector-detail.service");
const upsert_vector_detail_dto_1 = require("./dto/upsert-vector-detail.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const vector_detail_response_dto_1 = require("./dto/vector-detail-response.dto");
let VectorDetailController = class VectorDetailController {
    vectorDetailService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(vectorDetailService) {
        this.vectorDetailService = vectorDetailService;
    }
    async upsertVectorDetails(payload) {
        const result = await this.vectorDetailService.upsertVectorDetails(payload.VectorDetails);
        return (0, api_response_dto_1.createSuccessResponse)('Vector detail records processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.VectorDetailController = VectorDetailController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(VectorDetailController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert vector detail records.' }),
    (0, swagger_1.ApiBody)({ type: upsert_vector_detail_dto_1.UpsertVectorDetailDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Vector detail records processed successfully.',
        type: vector_detail_response_dto_1.VectorDetailApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_vector_detail_dto_1.UpsertVectorDetailDto]),
    __metadata("design:returntype", Promise)
], VectorDetailController.prototype, "upsertVectorDetails", null);
exports.VectorDetailController = VectorDetailController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('vector-details'),
    __metadata("design:paramtypes", [vector_detail_service_1.VectorDetailService])
], VectorDetailController);
//# sourceMappingURL=vector-detail.controller.js.map