import { Repository } from 'typeorm';
import { JvsKpiDetail } from './jvs-kpi-detail.entity';
import { KpiDetailRecord } from './dto/upsert-kpi-details.dto';
import { JvsAccount } from '../accounts/jvs-account.entity';
import { JvsBusinessUnit } from '../business-units/jvs-business-unit.entity';
interface UnprocessedKpiDetailRecord extends KpiDetailRecord {
    message: string;
    companyAttempted: string | null;
    businessUnitAttempted: string | null;
}
interface UpsertKpiDetailsResult {
    processed: number;
    unprocessedRecords: UnprocessedKpiDetailRecord[];
}
export declare class KpiDetailsService {
    private readonly kpiDetailRepository;
    private readonly accountRepository;
    private readonly businessUnitRepository;
    constructor(kpiDetailRepository: Repository<JvsKpiDetail>, accountRepository: Repository<JvsAccount>, businessUnitRepository: Repository<JvsBusinessUnit>);
    upsertKpiDetails(records: KpiDetailRecord[]): Promise<UpsertKpiDetailsResult>;
}
export {};
