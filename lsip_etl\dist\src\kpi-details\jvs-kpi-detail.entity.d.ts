export declare class JvsKpiDetail {
    id: string;
    name: string | null;
    currencyIsoCode: string | null;
    companyId: string | null;
    aovRs: number | null;
    available: boolean | null;
    averageOrderValueProduct: number | null;
    averageOrderValue: number | null;
    businessUnitId: string | null;
    cacRs: number | null;
    cac: number | null;
    cm2: number | null;
    cm3OfNmv: number | null;
    cmInLocalitiesWith500Users: number | null;
    cumulativeBankAccounts: number | null;
    bankAccountsCreatedPerMonth: number | null;
    cashInBank: number | null;
    categoryId: string | null;
    contributionMarginProduct: number | null;
    averageDepositBankAccount: number | null;
    contributionMargin: number | null;
    numberOfCreditCardIssued: number | null;
    customerAcquisitonCost: number | null;
    dau: number | null;
    dtrFromSource: number | null;
    date: Date | null;
    cashBurnRsCr: number | null;
    cashBurn: number | null;
    ebitdaRsCr: number | null;
    ebitda: number | null;
    enable: boolean | null;
    firstPartyMix: number | null;
    firstParty: number | null;
    fulfillmentCostOfNmv: number | null;
    gmv: number | null;
    gmvFromPrivateLabels: number | null;
    gmvPerExistingBuyer: number | null;
    gmvPerFosCost: number | null;
    gmvPerNewBuyer: number | null;
    contributionMarginRsCr: number | null;
    grossMargin: number | null;
    grossMarginOfNmv: number | null;
    inventoryDays: number | null;
    latestFlag: boolean | null;
    m12GmvRetention: number | null;
    m12UserRetention: number | null;
    m1Repeat: number | null;
    m2GmvRetention: number | null;
    contributionMarginOfTakeRate: number | null;
    m2UserRetention: number | null;
    m6GmvRetention: number | null;
    m6UserRetention: number | null;
    discretionaryMarketing: number | null;
    grossMarginProduct: number | null;
    mau: number | null;
    marketingSpends: number | null;
    m2Retention: number | null;
    marketplaceFloat: number | null;
    merchantAcquisitionCost: number | null;
    nmv: number | null;
    nmvPerBuyerRs: number | null;
    noOfActiveTrucks: number | null;
    noOfDenseLanes: number | null;
    noOfDenseLocalities: number | null;
    noOfOrders: number | null;
    noOfPayingMerchants: number | null;
    noOfTrackedTrips: number | null;
    noOfTransactingBuyers: number | null;
    noOfTransactingUsers: number | null;
    noOfTransactions: number | null;
    macRs: number | null;
    numberOfBuyersNumber: number | null;
    numberOfLocalitiesWith500Users: number | null;
    numberOfNewUsers: number | null;
    numberOfTransactingUsersLakh: number | null;
    operatingCashBurnRsCr: number | null;
    orderFrequency: number | null;
    payableDays: number | null;
    payingMerchants: number | null;
    peopleGAOthersRsCr: number | null;
    mauLakh: number | null;
    marketing: number | null;
    receivableDays: number | null;
    recordTypeName: string | null;
    revenuetotal: number | null;
    salesCost: number | null;
    shippingCost: number | null;
    shippingCostOfNmv: number | null;
    takeRateGmv: number | null;
    takeRateRsCr: number | null;
    takeRate: number | null;
    takeRateByRevenueProduct: number | null;
    takeRateByRevenue: number | null;
    takeRateFromLocalitiesWith500User: number | null;
    takeRatePerUserRs: number | null;
    takeRateRevenueFromDenseLocalitie: number | null;
    takeRateRevenuePerExistingUser: number | null;
    takeRateRevenuePerNewUser: number | null;
    testKpi: number | null;
    totalPeopleCost: number | null;
    transactingBuyers: number | null;
    transactingStoresPerFos: number | null;
    transactingUserInLocalitiesWith500: number | null;
    typeOfKpi: string | null;
    workingCapital: number | null;
    key: string | null;
    numberOfBuyers: number | null;
    revenueRsCr: number | null;
    cm1: number | null;
    customerSupportBizOpsCost: number | null;
    underUtilizedFixedCostsInOperations: number | null;
    techProductCosts: number | null;
    sgA: number | null;
    buyerWithCredit: number | null;
    yim12MonthsAfterDpd: number | null;
    performanceMarketing: number | null;
    cmRsCr: number | null;
    takeRateOfLocalitiesWith500Users: number | null;
    performanceMarketingRsCr: number | null;
    cmInLocalities500UsersRsCr: number | null;
    payingUsers: number | null;
    m3PayingUserRetention: number | null;
    m3PayingRevenueRetention: number | null;
    numberOfAudioHeardPerDau: number | null;
    moArpu: number | null;
    totalArpu: number | null;
    cacDollar: number | null;
    m0Conversion: number | null;
    conversion: number | null;
    subscritpionIncome: number | null;
    marketingCost: number | null;
    employeeExpenseIndiaUs: number | null;
    ebitdaDollar: number | null;
    totalCashburn: number | null;
    nmvUsd: number | null;
    fmcgNmv: number | null;
    temanUlaNmv: number | null;
    othersNmv: number | null;
    takeRateNetMargin: number | null;
    takeRateFmcg: number | null;
    takeRateTermanUla: number | null;
    logisticsCost: number | null;
    fos: number | null;
    warehouse: number | null;
    cashInBankDollar: number | null;
    uniqueOrderingStores: number | null;
    m6StoreRetention: number | null;
    nmvPerStorePerMonth: number | null;
    aovDollar: number | null;
    ordersPerStorePerMonth: number | null;
    appRunningCostServerAnalyticsCos: number | null;
    marketingCostPercent: number | null;
    contentServingCost: number | null;
    otherBrandingExpenses: number | null;
    musicLicenseCost: number | null;
    employeeBenefitsExpenses: number | null;
    others: number | null;
    dauMau: number | null;
    retentionD30: number | null;
    averageTimeSpent: number | null;
    uniqueCreators: number | null;
    totalInstalls: number | null;
    organic: number | null;
    costPerInstall: number | null;
    revenueDollar: number | null;
    skilling: number | null;
    recruitment: number | null;
    marketingOthers: number | null;
    salary: number | null;
    newInstalls: number | null;
    profileComplete: number | null;
    uninstallProfileCompletes: number | null;
    screenedLeads: number | null;
    fromExisting: number | null;
    fromReactivated: number | null;
    fromNew: number | null;
    screenedLeadsMau: number | null;
    m3UserRetention: number | null;
    repeatMauToLifeToDateUserBase: number | null;
    blendedCacForUser: number | null;
    newEmployerAcquired: number | null;
    activeEmployer: number | null;
    jobsActivated: number | null;
    m3EmployerRetention: number | null;
    repeatEmployerToLifeToDateEmployer: number | null;
    blendedCacEmployer: number | null;
    communityMauOfAppMau: number | null;
    communityToCommunityM1D30D60Rete: number | null;
    overallEnquiriesReceivedMtdYtd: number | null;
    conversionRateTrailing3Months: number | null;
    newOrdersForTheMonth: number | null;
    gtvArrOnSaas: number | null;
    gmvDollar: number | null;
    takeRateDollar: number | null;
    deliveredGmvForTheMonth: number | null;
    cumulativeOpenOrderBookClosing: number | null;
    cmPercent: number | null;
    numberOfLspsShippersAdded: number | null;
    activeShippersLsps: number | null;
    fixedExpenses: number | null;
    gmvFromRepeatCustomers: number | null;
    newCustomersAcquired: number | null;
    activeCustomers: number | null;
    q3CustomerRetention: number | null;
    activeSuppliers: number | null;
    q3SupplierRetention: number | null;
    arValueInRsCrs: number | null;
    apValueInRsCrs: number | null;
    inventoryValueInRsCrs: number | null;
    netWcArApInvAdv: number | null;
    wcConversionDays: number | null;
    freeCash: number | null;
    endingCarr: number | null;
    newCarrUpsell: number | null;
    monthlyChurnDowngrade: number | null;
    noOfCustomersCarr: number | null;
    carrPerCustomer: number | null;
    endingArr: number | null;
    newArrUpsell: number | null;
    monthlyChurnDowngradeArr: number | null;
    numberOfCustomersArr: number | null;
    arrPerCustomer: number | null;
    arrAsAOfCarr: number | null;
    platform: number | null;
    cpaas: number | null;
    voice: number | null;
    services: number | null;
    grossProfit: number | null;
    operatingIncome: number | null;
    dso: number | null;
    netRetention: number | null;
    averageCarrPerNewCustomer: number | null;
    carrFte: number | null;
    arrFte: number | null;
    annualisedNetChurn: number | null;
    ruleOf40: number | null;
    netMagicNumber: number | null;
    salesCost000: number | null;
    netRevenue: number | null;
    otherRevenueEGShippingRevenueEtc: number | null;
    cogs: number | null;
    allDiscountsRefundsEtc: number | null;
    wastage: number | null;
    parkingFuelLastMile: number | null;
    packagingCost: number | null;
    hubOperationsCost: number | null;
    cm2PercentageOfNetrevenue: number | null;
    marketingCostPercentageOfNetrevenue: number | null;
    cm3PercentOfNetRevenue: number | null;
    numberOfOrders: number | null;
    existingBuyers: number | null;
    ordersPerBuyer: number | null;
    existingBuyersNoBuyerPermonth: number | null;
    noOfUniqueBuyers: number | null;
    existingBuyers000: number | null;
    gmvFromExistingBuyers: number | null;
    m3GmvRetention: number | null;
    gmvFromTop3Categories: number | null;
    grossBooking: number | null;
    newExpansion: number | null;
    downgradeChurn: number | null;
    ndr: number | null;
    salesMarketing: number | null;
    generalAdministrative: number | null;
    researchDevelopment: number | null;
    netBurn000: number | null;
    burnMultiple: number | null;
    avgNewDealSize: number | null;
    eventCreated: number | null;
    eventHosted: number | null;
    eventsWithLessThan500Participants: number | null;
    eventsWith500To1000Participants: number | null;
    eventsWithGreater1000Participants: number | null;
    roiOnMarketingSpends: number | null;
    roiOnSalesSpends: number | null;
    salesQuotaAttainment: number | null;
    peopleRelatedSpend: number | null;
    cashBurnDollar000: number | null;
    netDollarRetention: number | null;
    grossRetention: number | null;
    netSeatRetention: number | null;
    grossSeatRetention: number | null;
    logoRetention: number | null;
    totalSMSpend: number | null;
    sMEfficiency: number | null;
    newLogoArr: number | null;
    newLogoSeats: number | null;
    newLogo: number | null;
    newLogoAsp: number | null;
    newLogoAvgSeats: number | null;
    ebitdaBurn: number | null;
    totalBookings: number | null;
    global: number | null;
    smb: number | null;
    mm: number | null;
    enterprise: number | null;
    invoicedRevenue: number | null;
    gm: number | null;
    smbPercent: number | null;
    mmPercent: number | null;
    enterprisePercent: number | null;
    mlToMql: number | null;
    mqlToSql: number | null;
    sqlToClosed: number | null;
    grossRoi: number | null;
    outboundQuotaAttainment: number | null;
    enterpriseMmCac: number | null;
    smbInboundMarketingCac: number | null;
    saasLogoQ1Retention: number | null;
    saasRevenueQ1Retention: number | null;
    saasConversionSelfServeFunnel: number | null;
    marketplaceEnterpriseLogoQ1Reten: number | null;
    marketplaceEnterpriseRevenueQ1Re: number | null;
    marketplaceMmLogoQ1Retention: number | null;
    marketplaceMmRevenueQ1Retention: number | null;
    creatorEarnings: number | null;
    editorEarnings: number | null;
    cm1Percent: number | null;
    inboundQuotaAttainment: number | null;
    m3SpendPerCustomer: number | null;
    m3M0SpendPerCustomer: number | null;
    npas: number | null;
    moneyRemittedToIndiaOnZolve: number | null;
    discretionaryMarketingRsCr: number | null;
    takeRateOthers: number | null;
    subsRevenue: number | null;
    subsHostingCost: number | null;
    subsClientSupportCost: number | null;
    arrCarr: number | null;
    iarrContracted: number | null;
    employeesOnPlatformContracted: number | null;
    averageAcv: number | null;
    tradingVolume: number | null;
    tradingRevenue: number | null;
    cumulativeDownloads: number | null;
    cumulativeKycedUsers: number | null;
    numberOfActiveTraders: number | null;
    traderW24Retention: number | null;
    averageTradingFrequencyPerTrader: number | null;
    totalContractsValueArr: number | null;
    totalTerminalArr: number | null;
    terminalArrNetOfDiscounts: number | null;
    terminalArrAdded: number | null;
    terminalArrLost: number | null;
    nonRecurringRevenue: number | null;
    totalInstitutes: number | null;
    numberOfNewInstitutes: number | null;
    terminalArrInstitute: number | null;
    sMEfficiencyNetOfDiscounts: number | null;
    totalCreators: number | null;
    numberOfNewCreators: number | null;
    totalCustomers: number | null;
    terminalArrLostDollar: number | null;
    q2TripRetention: number | null;
    m6LspRetenAdjustedForPilotTrail: number | null;
    m6ShipperRetentionAdjustedTrials: number | null;
    numberOfSuppliersAdded: number | null;
    aumTotal: number | null;
    grossProfitPercent: number | null;
    aumCash: number | null;
    aumCpf: number | null;
    aumSrs: number | null;
    newInvestment: number | null;
    redemption: number | null;
    netNewMoneyInvested: number | null;
    existingClients: number | null;
    newClients: number | null;
    registeredAccounts: number | null;
    brokerAccounts: number | null;
    fundedClients: number | null;
    churnedClientsFromCompany: number | null;
    shareOfPeerReferrals: number | null;
    recurringAmount: number | null;
    gmClient: number | null;
    paybackPeriodOnCac: number | null;
    m6MedianNetInvested: number | null;
    cmPercentRevenue: number | null;
    expansionArr: number | null;
    expansionArrOverallArr: number | null;
    headcount: number | null;
    grossMarginDollar: number | null;
    income: number | null;
    arrCac: number | null;
    churn: number | null;
    ltvCac: number | null;
}
