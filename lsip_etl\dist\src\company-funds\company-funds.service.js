"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyFundsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_company_fund_entity_1 = require("./jvs-company-fund.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
let CompanyFundsService = class CompanyFundsService {
    companyFundRepository;
    accountRepository;
    constructor(companyFundRepository, accountRepository) {
        this.companyFundRepository = companyFundRepository;
        this.accountRepository = accountRepository;
    }
    async upsertCompanyFunds(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedAccountIds = Array.from(new Set(records.map((record) => record.account__c).filter((value) => Boolean(value))));
        const existingAccountIds = requestedAccountIds.length
            ? new Set((await this.accountRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedAccountIds) },
            })).map((account) => account.id))
            : new Set();
        const entities = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                accountAttempted: null,
            };
            const accountId = record.account__c && existingAccountIds.has(record.account__c) ? record.account__c : null;
            if (record.account__c && !accountId) {
                meta.accountAttempted = record.account__c;
                meta.issues.push(`Account ${record.account__c} not found; stored without account linkage.`);
            }
            const entity = this.companyFundRepository.create({
                id: record.id,
                isDeleted: record.isdeleted ?? null,
                name: record.name ?? null,
                currencyIsoCode: record.currencyisocode ?? null,
                createdDateRaw: record.createddate ?? null,
                createdById: record.createdbyid ?? null,
                lastModifiedDate: record.lastmodifieddate ? new Date(record.lastmodifieddate) : null,
                lastModifiedById: record.lastmodifiedbyid ?? null,
                systemModStamp: record.systemmodstamp ?? null,
                lastActivityDate: record.lastactivitydate ? new Date(record.lastactivitydate) : null,
                fundId: record.fund__c ?? null,
                accountId,
                boardMemberId: record.board_member__c ?? null,
                goingInCost: record.going_in_cost__c ?? null,
                goingInPostMoneyValueM: record.going_in_post_money_value_m__c ?? null,
                goingInPreMoneyValueM: record.going_in_pre_money_value_m__c ?? null,
                initialDateOfInvestment: record.initial_date_of_investment__c
                    ? new Date(record.initial_date_of_investment__c)
                    : null,
                initialInvestmentStage: record.initial_investment_stage__c ?? null,
                initialOwnership: record.initial_ownership__c ?? null,
                initialRevenueStage: record.initial_revenue_stage__c ?? null,
                initialRound: record.initial_round__c ?? null,
                leadPrimaryId: record.lead_primary__c ?? null,
                leadSecondaryId: record.lead_secondary__c ?? null,
                shortName: record.short_name__c ?? null,
                key: record.key__c ?? null,
            });
            metaById.set(record.id, meta);
            entities.push(entity);
        }
        if (entities.length > 0) {
            await this.companyFundRepository.save(entities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    accountAttempted: meta.accountAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.CompanyFundsService = CompanyFundsService;
exports.CompanyFundsService = CompanyFundsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_company_fund_entity_1.JvsCompanyFund)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CompanyFundsService);
//# sourceMappingURL=company-funds.service.js.map