"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiPlanActualController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const kpi_plan_actual_service_1 = require("./kpi-plan-actual.service");
const upsert_kpi_plan_actual_dto_1 = require("./dto/upsert-kpi-plan-actual.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const kpi_plan_actual_response_dto_1 = require("./dto/kpi-plan-actual-response.dto");
let KpiPlanActualController = class KpiPlanActualController {
    kpiPlanActualService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(kpiPlanActualService) {
        this.kpiPlanActualService = kpiPlanActualService;
    }
    async upsertKpiPlanActuals(payload) {
        const result = await this.kpiPlanActualService.upsertKpiPlanActuals(payload.KpiPlanActuals);
        return (0, api_response_dto_1.createSuccessResponse)('KPI plan actual records processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.KpiPlanActualController = KpiPlanActualController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(KpiPlanActualController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert KPI plan actual records.' }),
    (0, swagger_1.ApiBody)({ type: upsert_kpi_plan_actual_dto_1.UpsertKpiPlanActualDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'KPI plan actual records processed successfully.',
        type: kpi_plan_actual_response_dto_1.KpiPlanActualApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_kpi_plan_actual_dto_1.UpsertKpiPlanActualDto]),
    __metadata("design:returntype", Promise)
], KpiPlanActualController.prototype, "upsertKpiPlanActuals", null);
exports.KpiPlanActualController = KpiPlanActualController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('kpi-plan-actuals'),
    __metadata("design:paramtypes", [kpi_plan_actual_service_1.KpiPlanActualService])
], KpiPlanActualController);
//# sourceMappingURL=kpi-plan-actual.controller.js.map