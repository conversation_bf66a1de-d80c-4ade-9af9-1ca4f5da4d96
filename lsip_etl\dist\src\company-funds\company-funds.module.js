"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyFundsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const company_funds_controller_1 = require("./company-funds.controller");
const company_funds_service_1 = require("./company-funds.service");
const jvs_company_fund_entity_1 = require("./jvs-company-fund.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
let CompanyFundsModule = class CompanyFundsModule {
};
exports.CompanyFundsModule = CompanyFundsModule;
exports.CompanyFundsModule = CompanyFundsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_company_fund_entity_1.JvsCompanyFund, jvs_account_entity_1.JvsAccount])],
        controllers: [company_funds_controller_1.CompanyFundsController],
        providers: [company_funds_service_1.CompanyFundsService],
        exports: [company_funds_service_1.CompanyFundsService],
    })
], CompanyFundsModule);
//# sourceMappingURL=company-funds.module.js.map