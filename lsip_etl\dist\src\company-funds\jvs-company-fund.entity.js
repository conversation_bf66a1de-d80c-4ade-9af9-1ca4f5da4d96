"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsCompanyFund = void 0;
const typeorm_1 = require("typeorm");
let JvsCompanyFund = class JvsCompanyFund {
    id;
    isDeleted = null;
    name = null;
    currencyIsoCode = null;
    createdDateRaw = null;
    createdById = null;
    lastModifiedDate = null;
    lastModifiedById = null;
    systemModStamp = null;
    lastActivityDate = null;
    fundId = null;
    accountId = null;
    boardMemberId = null;
    goingInCost = null;
    goingInPostMoneyValueM = null;
    goingInPreMoneyValueM = null;
    initialDateOfInvestment = null;
    initialInvestmentStage = null;
    initialOwnership = null;
    initialRevenueStage = null;
    initialRound = null;
    leadPrimaryId = null;
    leadSecondaryId = null;
    shortName = null;
    key = null;
};
exports.JvsCompanyFund = JvsCompanyFund;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsCompanyFund.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'isdeleted', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "isDeleted", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'currencyisocode', type: 'varchar', length: 3, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "currencyIsoCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'createddate', type: 'text', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "createdDateRaw", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'createdbyid', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "createdById", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lastmodifieddate', type: 'timestamptz', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "lastModifiedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lastmodifiedbyid', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "lastModifiedById", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'systemmodstamp', type: 'text', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "systemModStamp", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lastactivitydate', type: 'date', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "lastActivityDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fund__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "fundId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'account__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "accountId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'board_member__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "boardMemberId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'going_in_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "goingInCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'going_in_post_money_value_m__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "goingInPostMoneyValueM", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'going_in_pre_money_value_m__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "goingInPreMoneyValueM", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initial_date_of_investment__c', type: 'date', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "initialDateOfInvestment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initial_investment_stage__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "initialInvestmentStage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initial_ownership__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "initialOwnership", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initial_revenue_stage__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "initialRevenueStage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initial_round__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "initialRound", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lead_primary__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "leadPrimaryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lead_secondary__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "leadSecondaryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'short_name__c', type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "shortName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'key__c', type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", Object)
], JvsCompanyFund.prototype, "key", void 0);
exports.JvsCompanyFund = JvsCompanyFund = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_company_fund' })
], JvsCompanyFund);
//# sourceMappingURL=jvs-company-fund.entity.js.map