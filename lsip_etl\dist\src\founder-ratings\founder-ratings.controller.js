"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FounderRatingsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const founder_ratings_service_1 = require("./founder-ratings.service");
const upsert_founder_ratings_dto_1 = require("./dto/upsert-founder-ratings.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const founder_ratings_response_dto_1 = require("./dto/founder-ratings-response.dto");
let FounderRatingsController = class FounderRatingsController {
    founderRatingsService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(founderRatingsService) {
        this.founderRatingsService = founderRatingsService;
    }
    async upsertFounderRatings(payload) {
        const result = await this.founderRatingsService.upsertFounderRatings(payload.FounderRatings);
        return (0, api_response_dto_1.createSuccessResponse)('Founder ratings processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.FounderRatingsController = FounderRatingsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(FounderRatingsController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert founder rating data.' }),
    (0, swagger_1.ApiBody)({ type: upsert_founder_ratings_dto_1.UpsertFounderRatingsDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Founder ratings processed successfully.',
        type: founder_ratings_response_dto_1.FounderRatingsApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_founder_ratings_dto_1.UpsertFounderRatingsDto]),
    __metadata("design:returntype", Promise)
], FounderRatingsController.prototype, "upsertFounderRatings", null);
exports.FounderRatingsController = FounderRatingsController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('founder-ratings'),
    __metadata("design:paramtypes", [founder_ratings_service_1.FounderRatingsService])
], FounderRatingsController);
//# sourceMappingURL=founder-ratings.controller.js.map