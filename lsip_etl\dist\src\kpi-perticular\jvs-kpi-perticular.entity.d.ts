export declare class JvsKpiPerticular {
    id: string;
    name: string | null;
    currencyIsoCode: string | null;
    businessUnitId: string | null;
    companyId: string | null;
    creationType: string | null;
    formulaId: string | null;
    formula: string | null;
    kpiMasterId: string | null;
    kpiParticularLevelNumber: number | null;
    kpiParticularLevel: string | null;
    nameSet: boolean | null;
    alternateName: string | null;
    planRequired: boolean | null;
    subBusinessUnitId: string | null;
    uniqueIdentifier: string | null;
    benchmarkId: string | null;
    industryId: string | null;
    kpiParticularType: string | null;
    subIndustryId: string | null;
    entityId: string | null;
}
