"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bootstrap = bootstrap;
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const app_module_1 = require("./app.module");
const swagger_config_1 = require("./config/swagger.config");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const logger = new common_1.Logger(`Main.ts`);
    const port = Number(process.env.PORT ?? 3000);
    const enableSwagger = process.env.NODE_ENV !== 'production';
    logger.log(`App running with PORT=${process.env.PORT ?? 'default'} NODE_ENV=${process.env.NODE_ENV ?? 'unknown'}`);
    if (enableSwagger) {
        swagger_config_1.SwaggerConfig.setup(app);
    }
    await app.listen(port);
}
bootstrap();
//# sourceMappingURL=main.js.map