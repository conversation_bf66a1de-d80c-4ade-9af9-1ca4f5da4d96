"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJvsVectorPlanActualTable1720829000140 = void 0;
class CreateJvsVectorPlanActualTable1720829000140 {
    name = 'CreateJvsVectorPlanActualTable1720829000140';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_vector_plan_actual (
              id character varying(18) NOT NULL,
              name character varying(80),
              currencyisocode character varying(3),
              actual__c character varying(255),
              month1__c character varying(255),
              name__c character varying(255),
              number_of_plan_revisions__c double precision,
              plan__c character varying(255),
              quarter__c character varying(255),
              reason_for_deviation_remarks__c character varying(255),
              unique_identifier__c character varying(255),
              vector_detail__c character varying(18),
              year__c character varying(4),
              month_number__c double precision,
              consolidated_date__c character varying(255),
              kpi_plan_actual__c character varying(18),
              plan_actual_date__c date,
              CONSTRAINT pk_jvs_vector_plan_actual_id PRIMARY KEY (id)
            );
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_vector_plan_actual_vector_detail__c ON jvs_vector_plan_actual (vector_detail__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_vector_plan_actual_kpi_plan_actual__c ON jvs_vector_plan_actual (kpi_plan_actual__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_vector_plan_actual_plan_actual_date__c ON jvs_vector_plan_actual (plan_actual_date__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_vector_plan_actual_year__c ON jvs_vector_plan_actual (year__c);
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_vector_plan_actual ADD CONSTRAINT fk_jvs_vector_plan_actual_vector_detail__c FOREIGN KEY (vector_detail__c) REFERENCES jvs_vector_detail(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_vector_plan_actual ADD CONSTRAINT fk_jvs_vector_plan_actual_kpi_plan_actual__c FOREIGN KEY (kpi_plan_actual__c) REFERENCES jvs_kpi_plan_actual(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE jvs_vector_plan_actual DROP CONSTRAINT IF EXISTS fk_jvs_vector_plan_actual_kpi_plan_actual__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_vector_plan_actual DROP CONSTRAINT IF EXISTS fk_jvs_vector_plan_actual_vector_detail__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_vector_plan_actual_year__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_vector_plan_actual_plan_actual_date__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_vector_plan_actual_kpi_plan_actual__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_vector_plan_actual_vector_detail__c;
    `);
        await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_vector_plan_actual;
    `);
    }
}
exports.CreateJvsVectorPlanActualTable1720829000140 = CreateJvsVectorPlanActualTable1720829000140;
//# sourceMappingURL=1720829000140-CreateJvsVectorPlanActualTable.js.map