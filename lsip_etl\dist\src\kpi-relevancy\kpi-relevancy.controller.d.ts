import { KpiRelevancyService } from './kpi-relevancy.service';
import { UpsertKpiRelevancyDto } from './dto/upsert-kpi-relevancy.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class KpiRelevancyController {
    private readonly kpiRelevancyService;
    private static readonly validationPipe;
    constructor(kpiRelevancyService: KpiRelevancyService);
    upsertKpiRelevancies(payload: UpsertKpiRelevancyDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            companyAttempted: string | null;
            industryMasterAttempted: string | null;
            kpiMasterAttempted: string | null;
            subIndustryAttempted: string | null;
        }[];
    }>>;
}
