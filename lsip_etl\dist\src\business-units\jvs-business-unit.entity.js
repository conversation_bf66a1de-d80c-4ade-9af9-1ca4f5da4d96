"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsBusinessUnit = void 0;
const typeorm_1 = require("typeorm");
let JvsBusinessUnit = class JvsBusinessUnit {
    id;
    name = null;
    companyId = null;
    parentBusinessId = null;
    active = null;
    benchmarkId = null;
    type = null;
};
exports.JvsBusinessUnit = JvsBusinessUnit;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsBusinessUnit.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessUnit.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessUnit.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'parent_business__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessUnit.prototype, "parentBusinessId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'active__c', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessUnit.prototype, "active", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'benchmark__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessUnit.prototype, "benchmarkId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'type__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsBusinessUnit.prototype, "type", void 0);
exports.JvsBusinessUnit = JvsBusinessUnit = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_business_units' })
], JvsBusinessUnit);
//# sourceMappingURL=jvs-business-unit.entity.js.map