"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsKpiPerticular = void 0;
const typeorm_1 = require("typeorm");
let JvsKpiPerticular = class JvsKpiPerticular {
    id;
    name = null;
    currencyIsoCode = null;
    businessUnitId = null;
    companyId = null;
    creationType = null;
    formulaId = null;
    formula = null;
    kpiMasterId = null;
    kpiParticularLevelNumber = null;
    kpiParticularLevel = null;
    nameSet = null;
    alternateName = null;
    planRequired = null;
    subBusinessUnitId = null;
    uniqueIdentifier = null;
    benchmarkId = null;
    industryId = null;
    kpiParticularType = null;
    subIndustryId = null;
    entityId = null;
};
exports.JvsKpiPerticular = JvsKpiPerticular;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsKpiPerticular.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'currencyisocode', type: 'varchar', length: 3, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "currencyIsoCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'business_unit__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "businessUnitId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'creation_type__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "creationType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'formulaid__c', type: 'varchar', length: 32768, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "formulaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'formula__c', type: 'varchar', length: 131072, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "formula", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kpi_master__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "kpiMasterId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kpi_particular_level_number__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "kpiParticularLevelNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kpi_particular_level__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "kpiParticularLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'name_set__c', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "nameSet", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'name__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "alternateName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'plan_required__c', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "planRequired", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sub_business_unit__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "subBusinessUnitId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'unique_identifier__c', type: 'varchar', length: 225, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "uniqueIdentifier", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'benchmark__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "benchmarkId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'industry__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "industryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kpi_particular_type__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "kpiParticularType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sub_industry__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "subIndustryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'entity_id__c', type: 'varchar', length: 1300, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPerticular.prototype, "entityId", void 0);
exports.JvsKpiPerticular = JvsKpiPerticular = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_kpi_perticular' })
], JvsKpiPerticular);
//# sourceMappingURL=jvs-kpi-perticular.entity.js.map