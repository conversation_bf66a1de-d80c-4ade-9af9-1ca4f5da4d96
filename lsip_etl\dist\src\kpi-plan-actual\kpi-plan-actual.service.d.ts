import { Repository } from 'typeorm';
import { JvsKpiPlanActual } from './jvs-kpi-plan-actual.entity';
import { KpiPlanActualRecord } from './dto/upsert-kpi-plan-actual.dto';
import { JvsKpiPerticular } from '../kpi-perticular/jvs-kpi-perticular.entity';
interface UnprocessedKpiPlanActualRecord extends KpiPlanActualRecord {
    message: string;
    kpiPerticularAttempted: string | null;
}
interface UpsertKpiPlanActualResult {
    processed: number;
    unprocessedRecords: UnprocessedKpiPlanActualRecord[];
}
export declare class KpiPlanActualService {
    private readonly kpiPlanActualRepository;
    private readonly kpiPerticularRepository;
    constructor(kpiPlanActualRepository: Repository<JvsKpiPlanActual>, kpiPerticularRepository: Repository<JvsKpiPerticular>);
    upsertKpiPlanActuals(records: KpiPlanActualRecord[]): Promise<UpsertKpiPlanActualResult>;
}
export {};
