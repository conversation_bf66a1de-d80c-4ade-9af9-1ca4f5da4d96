"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorPlanActualApiResponseDto = exports.VectorPlanActualProcessedDataDto = exports.UnprocessedVectorPlanActualRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_vector_plan_actual_dto_1 = require("./upsert-vector-plan-actual.dto");
class UnprocessedVectorPlanActualRecordDto extends upsert_vector_plan_actual_dto_1.VectorPlanActualRecordDto {
    message;
    vectorDetailAttempted;
    kpiPlanActualAttempted;
}
exports.UnprocessedVectorPlanActualRecordDto = UnprocessedVectorPlanActualRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedVectorPlanActualRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Vector detail identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedVectorPlanActualRecordDto.prototype, "vectorDetailAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KPI plan actual identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedVectorPlanActualRecordDto.prototype, "kpiPlanActualAttempted", void 0);
class VectorPlanActualProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.VectorPlanActualProcessedDataDto = VectorPlanActualProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], VectorPlanActualProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings.',
        type: () => [UnprocessedVectorPlanActualRecordDto],
    }),
    __metadata("design:type", Array)
], VectorPlanActualProcessedDataDto.prototype, "unprocessedRecords", void 0);
class VectorPlanActualApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.VectorPlanActualApiResponseDto = VectorPlanActualApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: VectorPlanActualProcessedDataDto }),
    __metadata("design:type", VectorPlanActualProcessedDataDto)
], VectorPlanActualApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=vector-plan-actual-response.dto.js.map