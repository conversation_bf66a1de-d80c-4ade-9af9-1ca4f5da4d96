import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { KpiPerticularRecordDto } from './upsert-kpi-perticular.dto';
export declare class UnprocessedKpiPerticularRecordDto extends KpiPerticularRecordDto {
    message: string;
    accountAttempted: string | null;
    businessUnitAttempted: string | null;
    subBusinessUnitAttempted: string | null;
    kpiMasterAttempted: string | null;
    industryAttempted: string | null;
    subIndustryAttempted: string | null;
}
export declare class KpiPerticularProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedKpiPerticularRecordDto[];
}
export declare class KpiPerticularApiResponseDto extends BaseApiResponseDto {
    data: KpiPerticularProcessedDataDto;
}
