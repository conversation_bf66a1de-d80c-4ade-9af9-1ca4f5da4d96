"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessRatingsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_business_rating_entity_1 = require("./jvs-business-rating.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
let BusinessRatingsService = class BusinessRatingsService {
    businessRatingRepository;
    accountRepository;
    constructor(businessRatingRepository, accountRepository) {
        this.businessRatingRepository = businessRatingRepository;
        this.accountRepository = accountRepository;
    }
    async upsertBusinessRatings(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedAccountIds = Array.from(new Set(records.map((record) => record.account__c).filter((value) => Boolean(value))));
        const existingAccountIds = requestedAccountIds.length
            ? new Set((await this.accountRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedAccountIds) },
            })).map((account) => account.id))
            : new Set();
        const entities = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                accountAttempted: null,
            };
            const accountId = record.account__c && existingAccountIds.has(record.account__c) ? record.account__c : null;
            if (record.account__c && !accountId) {
                meta.accountAttempted = record.account__c;
                meta.issues.push(`Account ${record.account__c} not found; stored without account linkage.`);
            }
            const entity = this.businessRatingRepository.create({
                id: record.id,
                name: record.name ?? null,
                createdDate: record.createddate ? new Date(record.createddate) : null,
                accountId,
                productVisionStrategy: record.product_vision_strategy__c ?? null,
                productVisionStrategyDescription: record.product_vision_strategy_des__c ?? null,
                gtmMuscleEntSalesPartnershipDescription: record.gtm_muscle_ent_sales_partnership_des__c ?? null,
                gtmMuscleEntSalesPartnership: record.gtm_muscle_ent_sales_partnership__c ?? null,
                dataTechnologyOrientation: record.data_technology_orientation__c ?? null,
                dataTechnologyOrientationDescription: record.data_technology_orientation_des__c ?? null,
                deepTechEnggProductLeadership: record.deep_tech_engg_product_leadership__c ?? null,
                deepTechEnggProductLeadershipDescription: record.deep_tech_engg_product_leadership_des__c ?? null,
                gtmMuscleCommercialization: record.gtm_muscle_commercialization__c ?? null,
                gtmMuscleCommercializationDescription: record.gtm_muscle_commercialization_des__c ?? null,
                regulatory: record.regulatory__c ?? null,
                regulatoryDescription: record.regulatory_des__c ?? null,
                productVisionStrategyAiMl: record.product_vision_strategy_ai_ml__c ?? null,
                productVisionStrategyAiMlDescription: record.product_vision_strategy_ai_ml_des__c ?? null,
                gtmMuscleForDevtoolsProsumer: record.gtm_muscle_for_devtools_prosumer__c ?? null,
                gtmMuscleForDevtoolsProsumerDescription: record.gtm_muscle_for_devtools_prosumer_des__c ?? null,
                narrativeBuilding: record.narrative_building__c ?? null,
                narrativeBuildingDescription: record.narrative_building_desc__c ?? null,
                productVisionStrategyAiLlm: record.product_vision_strategy_ai_llm__c ?? null,
                productVisionStrategyAiLlmDescription: record.product_vision_strategy_ai_llm_desc__c ?? null,
                gtmMuscle: record.gtm_muscle__c ?? null,
                gtmMuscleDescription: record.gtm_muscle_desc__c ?? null,
                positioningNarrativeBuilding: record.positioning_narrative_building__c ?? null,
                positioningNarrativeBuildingDescription: record.positioning_narrative_building_desc__c ?? null,
                productStrategyStrategicThinking: record.product_strategy_strategic_thinking__c ?? null,
                productStrategyStrategicThinkingDescription: record.product_strategy_strategic_thinking_des__c ?? null,
                marketingNarrativeBuilding: record.marketing_narrative_building__c ?? null,
                marketingNarrativeBuildingDescription: record.marketing_narrative_building_desc__c ?? null,
                aiCapability: record.ai_capability__c ?? null,
                aiCapabilityDescription: record.ai_capability_desc__c ?? null,
                categoryCreation: record.category_creation__c ?? null,
                categoryCreationDescription: record.category_creation_desc__c ?? null,
            });
            metaById.set(record.id, meta);
            entities.push(entity);
        }
        if (entities.length > 0) {
            await this.businessRatingRepository.save(entities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    accountAttempted: meta.accountAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.BusinessRatingsService = BusinessRatingsService;
exports.BusinessRatingsService = BusinessRatingsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_business_rating_entity_1.JvsBusinessRating)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], BusinessRatingsService);
//# sourceMappingURL=business-ratings.service.js.map