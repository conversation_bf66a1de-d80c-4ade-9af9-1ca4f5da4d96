"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_account_entity_1 = require("./jvs-account.entity");
const jvs_industry_entity_1 = require("../industries/jvs-industry.entity");
let AccountsService = class AccountsService {
    accountRepository;
    industryRepository;
    constructor(accountRepository, industryRepository) {
        this.accountRepository = accountRepository;
        this.industryRepository = industryRepository;
    }
    async upsertAccounts(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const recordMeta = new Map();
        const requestedIndustryIds = new Set();
        for (const record of records) {
            if (record.industry_master__c) {
                requestedIndustryIds.add(record.industry_master__c);
            }
            if (record.sub_industry__c) {
                requestedIndustryIds.add(record.sub_industry__c);
            }
        }
        const existingIndustryIds = requestedIndustryIds.size
            ? new Set((await this.industryRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedIndustryIds]) },
            })).map((industry) => industry.id))
            : new Set();
        const topLevelEntities = [];
        const childCandidates = [];
        for (const record of records) {
            const original = { ...record };
            const meta = {
                original,
                issues: [],
                parentAccountAttempted: null,
                industryMasterAttempted: null,
                subIndustryAttempted: null,
            };
            const industryMasterId = record.industry_master__c && existingIndustryIds.has(record.industry_master__c)
                ? record.industry_master__c
                : null;
            if (record.industry_master__c && !industryMasterId) {
                meta.industryMasterAttempted = record.industry_master__c;
                meta.issues.push(`Industry master ${record.industry_master__c} not found; stored without industry linkage.`);
            }
            const subIndustryId = record.sub_industry__c && existingIndustryIds.has(record.sub_industry__c)
                ? record.sub_industry__c
                : null;
            if (record.sub_industry__c && !subIndustryId) {
                meta.subIndustryAttempted = record.sub_industry__c;
                meta.issues.push(`Sub-industry ${record.sub_industry__c} not found; stored without sub-industry linkage.`);
            }
            const entity = this.accountRepository.create({
                id: record.id,
                masterRecordId: record.masterrecordid ?? null,
                name: record.name ?? null,
                type: record.type ?? null,
                parentId: null,
                phone: record.phone ?? null,
                website: record.website ?? null,
                industry: record.industry ?? null,
                description: record.description ?? null,
                currencyIsoCode: record.currencyisocode ?? null,
                isPartner: record.ispartner ?? null,
                boardPositionHeld: record.board_position_held__c ?? null,
                companyFullName: record.company_full_name__c ?? null,
                goingInCost: record.going_in_cost__c ?? null,
                goingInPostMoneyValue: record.going_in_post_money_value__c ?? null,
                goingInPreMoney: record.going_in_pre_money__c ?? null,
                hqGeography: record.hq_geography__c ?? null,
                initialInvestmentStage: record.initial_investment_stage__c ?? null,
                initialOwnership: record.initial_ownership__c ?? null,
                initialRevenueStage: record.initial_revenue_stage__c ?? null,
                initialRound: record.initial_round__c ?? null,
                lsipInitialFinancingRound: record.lsip_initial_financing_roung__c ?? null,
                lead: record.lead__c ?? null,
                legalEntityName: record.legal_entity_name__c ?? null,
                legalGeography: record.legal_geography__c ?? null,
                sector: record.sector__c ?? null,
                shortName: record.short_name__c ?? null,
                secondLead: record.x2nd_lead__c ?? null,
                documentation: record.documentation__c ?? null,
                companyStatus: record.company_status__c ?? null,
                region: record.region__c ?? null,
                currencyFormat: record.currency_format__c ?? null,
                industryMasterId,
                subIndustryId,
                regionCountry: record.region_country__c ?? null,
                auditor: record.auditor__c ?? null,
                benchmark: record.benchmark__c ?? null,
                entity: record.entity__c ?? null,
                business: record.business__c ?? null,
                year: record.year__c ?? null
            });
            recordMeta.set(record.id, meta);
            const parentId = record.parentid ?? null;
            if (parentId) {
                childCandidates.push({ entity, parentId });
            }
            else {
                topLevelEntities.push(entity);
            }
        }
        if (topLevelEntities.length > 0) {
            await this.accountRepository.save(topLevelEntities);
        }
        const requestedParentIds = Array.from(new Set(childCandidates.map((candidate) => candidate.parentId)));
        const existingParents = requestedParentIds.length
            ? await this.accountRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedParentIds) },
            })
            : [];
        const resolvedAccountIds = new Set([
            ...existingParents.map((account) => account.id),
            ...topLevelEntities.map((account) => account.id),
        ]);
        const childEntities = [];
        let pendingCandidates = [...childCandidates];
        while (pendingCandidates.length > 0) {
            const deferredCandidates = [];
            let processedInCurrentPass = false;
            for (const candidate of pendingCandidates) {
                if (resolvedAccountIds.has(candidate.parentId)) {
                    candidate.entity.parentId = candidate.parentId;
                    resolvedAccountIds.add(candidate.entity.id);
                    childEntities.push(candidate.entity);
                    processedInCurrentPass = true;
                }
                else {
                    deferredCandidates.push(candidate);
                }
            }
            if (!processedInCurrentPass) {
                for (const candidate of deferredCandidates) {
                    candidate.entity.parentId = null;
                    childEntities.push(candidate.entity);
                    const meta = recordMeta.get(candidate.entity.id);
                    if (meta) {
                        meta.parentAccountAttempted = candidate.parentId;
                        meta.issues.push(`Parent account ${candidate.parentId} not found; stored without parent reference.`);
                    }
                }
                break;
            }
            pendingCandidates = deferredCandidates;
        }
        if (childEntities.length > 0) {
            await this.accountRepository.save(childEntities);
        }
        const unprocessedRecords = [];
        for (const meta of recordMeta.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    parentAccountAttempted: meta.parentAccountAttempted,
                    industryMasterAttempted: meta.industryMasterAttempted,
                    subIndustryAttempted: meta.subIndustryAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.AccountsService = AccountsService;
exports.AccountsService = AccountsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_industry_entity_1.JvsIndustry)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AccountsService);
//# sourceMappingURL=accounts.service.js.map