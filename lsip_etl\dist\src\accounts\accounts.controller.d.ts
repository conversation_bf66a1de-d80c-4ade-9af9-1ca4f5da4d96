import { AccountsService } from './accounts.service';
import { UpsertAccountsDto } from './dto/upsert-accounts.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class AccountsController {
    private readonly accountsService;
    private static readonly validationPipe;
    constructor(accountsService: AccountsService);
    upsertAccounts(payload: UpsertAccountsDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            parentAccountAttempted: string | null;
            industryMasterAttempted: string | null;
            subIndustryAttempted: string | null;
        }[];
    }>>;
}
