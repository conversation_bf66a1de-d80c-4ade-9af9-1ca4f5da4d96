import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { ContactRecordDto } from './upsert-contacts.dto';
export declare class UnprocessedContactRecordDto extends ContactRecordDto {
    message: string;
    accountAttempted: string | null;
    companyFundAttempted: string | null;
    reportsToAttempted: string | null;
}
export declare class ContactsProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedContactRecordDto[];
}
export declare class ContactsApiResponseDto extends BaseApiResponseDto {
    data: ContactsProcessedDataDto;
}
