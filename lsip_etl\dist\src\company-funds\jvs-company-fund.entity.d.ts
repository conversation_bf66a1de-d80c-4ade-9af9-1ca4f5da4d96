export declare class JvsCompanyFund {
    id: string;
    isDeleted: boolean | null;
    name: string | null;
    currencyIsoCode: string | null;
    createdDateRaw: string | null;
    createdById: string | null;
    lastModifiedDate: Date | null;
    lastModifiedById: string | null;
    systemModStamp: string | null;
    lastActivityDate: Date | null;
    fundId: string | null;
    accountId: string | null;
    boardMemberId: string | null;
    goingInCost: number | null;
    goingInPostMoneyValueM: number | null;
    goingInPreMoneyValueM: number | null;
    initialDateOfInvestment: Date | null;
    initialInvestmentStage: string | null;
    initialOwnership: number | null;
    initialRevenueStage: string | null;
    initialRound: string | null;
    leadPrimaryId: string | null;
    leadSecondaryId: string | null;
    shortName: string | null;
    key: string | null;
}
