"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiMasterController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const kpi_master_service_1 = require("./kpi-master.service");
const upsert_kpi_master_dto_1 = require("./dto/upsert-kpi-master.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const kpi_master_response_dto_1 = require("./dto/kpi-master-response.dto");
let KpiMasterController = class KpiMasterController {
    kpiMasterService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(kpiMasterService) {
        this.kpiMasterService = kpiMasterService;
    }
    async upsertKpiMasters(payload) {
        const result = await this.kpiMasterService.upsertKpiMasters(payload.KpiMasters);
        return (0, api_response_dto_1.createSuccessResponse)('KPI master records processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.KpiMasterController = KpiMasterController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(KpiMasterController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert KPI master records.' }),
    (0, swagger_1.ApiBody)({ type: upsert_kpi_master_dto_1.UpsertKpiMasterDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'KPI master records processed successfully.',
        type: kpi_master_response_dto_1.KpiMasterApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_kpi_master_dto_1.UpsertKpiMasterDto]),
    __metadata("design:returntype", Promise)
], KpiMasterController.prototype, "upsertKpiMasters", null);
exports.KpiMasterController = KpiMasterController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('kpi-master'),
    __metadata("design:paramtypes", [kpi_master_service_1.KpiMasterService])
], KpiMasterController);
//# sourceMappingURL=kpi-master.controller.js.map