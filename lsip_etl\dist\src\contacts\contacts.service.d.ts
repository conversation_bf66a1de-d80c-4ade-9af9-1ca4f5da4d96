import { Repository } from 'typeorm';
import { JvsContact } from './jvs-contact.entity';
import { ContactRecord } from './dto/upsert-contacts.dto';
import { JvsAccount } from '../accounts/jvs-account.entity';
import { JvsCompanyFund } from '../company-funds/jvs-company-fund.entity';
interface UnprocessedContactRecord extends ContactRecord {
    message: string;
    accountAttempted: string | null;
    companyFundAttempted: string | null;
    reportsToAttempted: string | null;
}
interface UpsertContactsResult {
    processed: number;
    unprocessedRecords: UnprocessedContactRecord[];
}
export declare class ContactsService {
    private readonly contactRepository;
    private readonly accountRepository;
    private readonly companyFundRepository;
    constructor(contactRepository: Repository<JvsContact>, accountRepository: Repository<JvsAccount>, companyFundRepository: Repository<JvsCompanyFund>);
    upsertContacts(records: ContactRecord[]): Promise<UpsertContactsResult>;
}
export {};
