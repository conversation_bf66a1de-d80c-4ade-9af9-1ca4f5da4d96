"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJvsKpiRelevancyTable1720829000110 = void 0;
class CreateJvsKpiRelevancyTable1720829000110 {
    name = 'CreateJvsKpiRelevancyTable1720829000110';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_kpi_relevancy (
              id character varying(18) NOT NULL,
              name character varying(80),
              currencyisocode character varying(3),
              company__c character varying(18),
              industry_master__c character varying(18),
              kpi_master__c character varying(18),
              sub_industry__c character varying(18),
              benchmark__c character varying(18),
              CONSTRAINT pk_jvs_kpi_relevancy_id PRIMARY KEY (id)
            );
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_relevancy_company__c ON jvs_kpi_relevancy (company__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_relevancy_kpi_master__c ON jvs_kpi_relevancy (kpi_master__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_relevancy_industry_master__c ON jvs_kpi_relevancy (industry_master__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_relevancy_sub_industry__c ON jvs_kpi_relevancy (sub_industry__c);
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_relevancy ADD CONSTRAINT fk_jvs_kpi_relevancy_company__c FOREIGN KEY (company__c) REFERENCES jvs_accounts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_relevancy ADD CONSTRAINT fk_jvs_kpi_relevancy_kpi_master__c FOREIGN KEY (kpi_master__c) REFERENCES jvs_kpi_master(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_relevancy ADD CONSTRAINT fk_jvs_kpi_relevancy_industry_master__c FOREIGN KEY (industry_master__c) REFERENCES jvs_industries(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_relevancy ADD CONSTRAINT fk_jvs_kpi_relevancy_sub_industry__c FOREIGN KEY (sub_industry__c) REFERENCES jvs_industries(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_relevancy DROP CONSTRAINT IF EXISTS fk_jvs_kpi_relevancy_sub_industry__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_relevancy DROP CONSTRAINT IF EXISTS fk_jvs_kpi_relevancy_industry_master__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_relevancy DROP CONSTRAINT IF EXISTS fk_jvs_kpi_relevancy_kpi_master__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_relevancy DROP CONSTRAINT IF EXISTS fk_jvs_kpi_relevancy_company__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_relevancy_sub_industry__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_relevancy_industry_master__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_relevancy_kpi_master__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_relevancy_company__c;
    `);
        await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_kpi_relevancy;
    `);
    }
}
exports.CreateJvsKpiRelevancyTable1720829000110 = CreateJvsKpiRelevancyTable1720829000110;
//# sourceMappingURL=1720829000110-CreateJvsKpiRelevancyTable.js.map