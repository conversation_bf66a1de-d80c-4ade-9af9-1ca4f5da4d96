import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { FounderRatingRecordDto } from './upsert-founder-ratings.dto';
export declare class UnprocessedFounderRatingRecordDto extends FounderRatingRecordDto {
    message: string;
    accountAttempted: string | null;
}
export declare class FounderRatingsProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedFounderRatingRecordDto[];
}
export declare class FounderRatingsApiResponseDto extends BaseApiResponseDto {
    data: FounderRatingsProcessedDataDto;
}
