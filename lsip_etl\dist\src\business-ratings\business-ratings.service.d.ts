import { Repository } from 'typeorm';
import { JvsBusinessRating } from './jvs-business-rating.entity';
import { BusinessRatingRecord } from './dto/upsert-business-ratings.dto';
import { JvsAccount } from '../accounts/jvs-account.entity';
interface UnprocessedBusinessRatingRecord extends BusinessRatingRecord {
    message: string;
    accountAttempted: string | null;
}
interface UpsertBusinessRatingsResult {
    processed: number;
    unprocessedRecords: UnprocessedBusinessRatingRecord[];
}
export declare class BusinessRatingsService {
    private readonly businessRatingRepository;
    private readonly accountRepository;
    constructor(businessRatingRepository: Repository<JvsBusinessRating>, accountRepository: Repository<JvsAccount>);
    upsertBusinessRatings(records: BusinessRatingRecord[]): Promise<UpsertBusinessRatingsResult>;
}
export {};
