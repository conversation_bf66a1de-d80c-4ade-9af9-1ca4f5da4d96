"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorMasterController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const vector_master_service_1 = require("./vector-master.service");
const upsert_vector_master_dto_1 = require("./dto/upsert-vector-master.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const vector_master_response_dto_1 = require("./dto/vector-master-response.dto");
let VectorMasterController = class VectorMasterController {
    vectorMasterService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(vectorMasterService) {
        this.vectorMasterService = vectorMasterService;
    }
    async upsertVectorMasters(payload) {
        const result = await this.vectorMasterService.upsertVectorMasters(payload.VectorMasters);
        return (0, api_response_dto_1.createSuccessResponse)('Vector master records processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.VectorMasterController = VectorMasterController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(VectorMasterController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert vector master records.' }),
    (0, swagger_1.ApiBody)({ type: upsert_vector_master_dto_1.UpsertVectorMasterDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Vector master records processed successfully.',
        type: vector_master_response_dto_1.VectorMasterApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_vector_master_dto_1.UpsertVectorMasterDto]),
    __metadata("design:returntype", Promise)
], VectorMasterController.prototype, "upsertVectorMasters", null);
exports.VectorMasterController = VectorMasterController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('vector-masters'),
    __metadata("design:paramtypes", [vector_master_service_1.VectorMasterService])
], VectorMasterController);
//# sourceMappingURL=vector-master.controller.js.map