"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRootAsync({
                inject: [config_1.ConfigService],
                useFactory: (configService) => {
                    const sslMode = configService.get('database.sslMode');
                    const schema = configService.get('database.schema');
                    if (!schema) {
                        throw new Error('Database schema configuration (DB_SCHEMA) is missing');
                    }
                    const sslOption = (() => {
                        if (!sslMode || sslMode === 'disable') {
                            return false;
                        }
                        if (sslMode === 'verify-full') {
                            return { rejectUnauthorized: true };
                        }
                        return { rejectUnauthorized: false };
                    })();
                    const fileExtension = __filename.slice(__filename.lastIndexOf('.'));
                    const isRunningTs = fileExtension === '.ts';
                    return {
                        type: 'postgres',
                        host: configService.get('database.host'),
                        port: configService.get('database.port'),
                        username: configService.get('database.username'),
                        password: configService.get('database.password'),
                        database: configService.get('database.name'),
                        ssl: sslOption,
                        schema,
                        extra: {
                            options: `-c search_path=${schema}`,
                        },
                        autoLoadEntities: true,
                        synchronize: false,
                        migrations: isRunningTs ? ['src/migrations/*.ts'] : ['dist/migrations/*.js'],
                        migrationsTableName: 'migrations',
                    };
                },
            }),
        ],
        exports: [typeorm_1.TypeOrmModule],
    })
], DatabaseModule);
//# sourceMappingURL=database.module.js.map