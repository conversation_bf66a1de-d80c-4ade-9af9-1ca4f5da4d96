"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddYearFieldToAccount************* = void 0;
class AddYearFieldToAccount************* {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE jvs_accounts ADD COLUMN year__c varchar(255)`);
        await queryRunner.query(`ALTER TABLE jvs_accounts ALTER COLUMN isPartner TYPE BOOLEAN USING (isPartner::BOOLEAN);`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE jvs_accounts DROP COLUMN year__c`);
        await queryRunner.query(` ALTER TABLE jvs_accounts ALTER COLUMN isPartner TYPE VARCHAR(255) USING (isPartner::VARCHAR);`);
    }
}
exports.AddYearFieldToAccount************* = AddYearFieldToAccount*************;
//# sourceMappingURL=*************-AddYearFieldToAccount.js.map