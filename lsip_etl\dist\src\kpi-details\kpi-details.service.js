"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiDetailsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_kpi_detail_entity_1 = require("./jvs-kpi-detail.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
const jvs_business_unit_entity_1 = require("../business-units/jvs-business-unit.entity");
let KpiDetailsService = class KpiDetailsService {
    kpiDetailRepository;
    accountRepository;
    businessUnitRepository;
    constructor(kpiDetailRepository, accountRepository, businessUnitRepository) {
        this.kpiDetailRepository = kpiDetailRepository;
        this.accountRepository = accountRepository;
        this.businessUnitRepository = businessUnitRepository;
    }
    async upsertKpiDetails(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedCompanyIds = new Set(records.map((record) => record.companyId).filter((value) => Boolean(value)));
        const requestedBusinessUnitIds = new Set(records.map((record) => record.businessUnitId).filter((value) => Boolean(value)));
        const existingCompanyIds = requestedCompanyIds.size
            ? new Set((await this.accountRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedCompanyIds]) },
            })).map((account) => account.id))
            : new Set();
        const existingBusinessUnitIds = requestedBusinessUnitIds.size
            ? new Set((await this.businessUnitRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedBusinessUnitIds]) },
            })).map((unit) => unit.id))
            : new Set();
        const entities = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                companyAttempted: null,
                businessUnitAttempted: null,
            };
            const companyId = record.companyId && existingCompanyIds.has(record.companyId) ? record.companyId : null;
            if (record.companyId && !companyId) {
                meta.companyAttempted = record.companyId;
                meta.issues.push(`Account ${record.companyId} not found; stored without company linkage.`);
            }
            const businessUnitId = record.businessUnitId && existingBusinessUnitIds.has(record.businessUnitId)
                ? record.businessUnitId
                : null;
            if (record.businessUnitId && !businessUnitId) {
                meta.businessUnitAttempted = record.businessUnitId;
                meta.issues.push(`Business unit ${record.businessUnitId} not found; stored without business unit linkage.`);
            }
            const entity = this.kpiDetailRepository.create({
                ...record,
                companyId,
                businessUnitId,
            });
            entity.date = record.date ? new Date(record.date) : null;
            metaById.set(record.id, meta);
            entities.push(entity);
        }
        if (entities.length > 0) {
            await this.kpiDetailRepository.save(entities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    companyAttempted: meta.companyAttempted,
                    businessUnitAttempted: meta.businessUnitAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.KpiDetailsService = KpiDetailsService;
exports.KpiDetailsService = KpiDetailsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_kpi_detail_entity_1.JvsKpiDetail)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __param(2, (0, typeorm_1.InjectRepository)(jvs_business_unit_entity_1.JvsBusinessUnit)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], KpiDetailsService);
//# sourceMappingURL=kpi-details.service.js.map