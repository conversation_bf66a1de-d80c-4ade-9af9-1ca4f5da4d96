import { KpiMasterService } from './kpi-master.service';
import { UpsertKpiMasterDto } from './dto/upsert-kpi-master.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class KpiMasterController {
    private readonly kpiMasterService;
    private static readonly validationPipe;
    constructor(kpiMasterService: KpiMasterService);
    upsertKpiMasters(payload: UpsertKpiMasterDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: never[];
    }>>;
}
