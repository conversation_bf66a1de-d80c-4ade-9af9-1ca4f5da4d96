"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiMasterService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_kpi_master_entity_1 = require("./jvs-kpi-master.entity");
let KpiMasterService = class KpiMasterService {
    kpiMasterRepository;
    constructor(kpiMasterRepository) {
        this.kpiMasterRepository = kpiMasterRepository;
    }
    async upsertKpiMasters(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const entities = records.map((record) => this.kpiMasterRepository.create({
            id: record.id,
            name: record.name ?? null,
            currencyIsoCode: record.currencyisocode ?? null,
            category: record.category__c ?? null,
            type: record.type__c ?? null,
            unit: record.unit__c ?? null,
        }));
        await this.kpiMasterRepository.save(entities);
        return {
            processed: records.length,
            unprocessedRecords: [],
        };
    }
};
exports.KpiMasterService = KpiMasterService;
exports.KpiMasterService = KpiMasterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_kpi_master_entity_1.JvsKpiMaster)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], KpiMasterService);
//# sourceMappingURL=kpi-master.service.js.map