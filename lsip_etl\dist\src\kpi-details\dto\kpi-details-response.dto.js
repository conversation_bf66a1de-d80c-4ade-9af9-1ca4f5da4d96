"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiDetailsApiResponseDto = exports.KpiDetailsProcessedDataDto = exports.UnprocessedKpiDetailRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_kpi_details_dto_1 = require("./upsert-kpi-details.dto");
class UnprocessedKpiDetailRecordDto extends upsert_kpi_details_dto_1.KpiDetailRecordDto {
    message;
    companyAttempted;
    businessUnitAttempted;
}
exports.UnprocessedKpiDetailRecordDto = UnprocessedKpiDetailRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedKpiDetailRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiDetailRecordDto.prototype, "companyAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Business unit identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiDetailRecordDto.prototype, "businessUnitAttempted", void 0);
class KpiDetailsProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.KpiDetailsProcessedDataDto = KpiDetailsProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], KpiDetailsProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings (e.g., unresolved relationships).',
        type: () => [UnprocessedKpiDetailRecordDto],
    }),
    __metadata("design:type", Array)
], KpiDetailsProcessedDataDto.prototype, "unprocessedRecords", void 0);
class KpiDetailsApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.KpiDetailsApiResponseDto = KpiDetailsApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: KpiDetailsProcessedDataDto }),
    __metadata("design:type", KpiDetailsProcessedDataDto)
], KpiDetailsApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=kpi-details-response.dto.js.map