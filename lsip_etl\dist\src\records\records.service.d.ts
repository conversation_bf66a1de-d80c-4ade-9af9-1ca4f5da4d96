import { Repository } from 'typeorm';
import { JvsAccount } from '../accounts/jvs-account.entity';
import { JvsBusinessRating } from '../business-ratings/jvs-business-rating.entity';
import { JvsBusinessUnit } from '../business-units/jvs-business-unit.entity';
import { JvsCompanyFund } from '../company-funds/jvs-company-fund.entity';
import { JvsContact } from '../contacts/jvs-contact.entity';
import { JvsFounderRating } from '../founder-ratings/jvs-founder-rating.entity';
import { JvsIndustry } from '../industries/jvs-industry.entity';
import { JvsKpiDetail } from '../kpi-details/jvs-kpi-detail.entity';
import { JvsKpiMaster } from '../kpi-master/jvs-kpi-master.entity';
import { JvsKpiPerticular } from '../kpi-perticular/jvs-kpi-perticular.entity';
import { JvsKpiPlanActual } from '../kpi-plan-actual/jvs-kpi-plan-actual.entity';
import { JvsKpiRelevancy } from '../kpi-relevancy/jvs-kpi-relevancy.entity';
import { JvsVectorMaster } from '../vector-master/jvs-vector-master.entity';
import { JvsVectorDetail } from '../vector-detail/jvs-vector-detail.entity';
import { JvsVectorPlanActual } from '../vector-plan-actual/jvs-vector-plan-actual.entity';
import { RecordsSummaryDataDto, SyncedRecordsDataDto } from './dto/records-summary.dto';
export declare class RecordsService {
    private readonly accountsRepository;
    private readonly businessRatingsRepository;
    private readonly businessUnitsRepository;
    private readonly companyFundsRepository;
    private readonly contactsRepository;
    private readonly founderRatingsRepository;
    private readonly industriesRepository;
    private readonly kpiDetailsRepository;
    private readonly kpiMasterRepository;
    private readonly kpiPerticularRepository;
    private readonly kpiPlanActualRepository;
    private readonly kpiRelevancyRepository;
    private readonly vectorMasterRepository;
    private readonly vectorDetailRepository;
    private readonly vectorPlanActualRepository;
    constructor(accountsRepository: Repository<JvsAccount>, businessRatingsRepository: Repository<JvsBusinessRating>, businessUnitsRepository: Repository<JvsBusinessUnit>, companyFundsRepository: Repository<JvsCompanyFund>, contactsRepository: Repository<JvsContact>, founderRatingsRepository: Repository<JvsFounderRating>, industriesRepository: Repository<JvsIndustry>, kpiDetailsRepository: Repository<JvsKpiDetail>, kpiMasterRepository: Repository<JvsKpiMaster>, kpiPerticularRepository: Repository<JvsKpiPerticular>, kpiPlanActualRepository: Repository<JvsKpiPlanActual>, kpiRelevancyRepository: Repository<JvsKpiRelevancy>, vectorMasterRepository: Repository<JvsVectorMaster>, vectorDetailRepository: Repository<JvsVectorDetail>, vectorPlanActualRepository: Repository<JvsVectorPlanActual>);
    private getTrackedRepositories;
    private collectCounts;
    getRecordsSummary(): Promise<RecordsSummaryDataDto>;
    getSyncedRecords(includeZero: boolean): Promise<SyncedRecordsDataDto>;
}
