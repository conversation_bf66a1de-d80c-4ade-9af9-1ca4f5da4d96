"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertAccountsDto = exports.AccountRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class AccountRecordDto {
    id;
    masterrecordid;
    name;
    type;
    parentid;
    phone;
    website;
    industry;
    description;
    currencyisocode;
    ispartner;
    board_position_held__c;
    company_full_name__c;
    going_in_cost__c;
    going_in_post_money_value__c;
    going_in_pre_money__c;
    hq_geography__c;
    initial_investment_stage__c;
    initial_ownership__c;
    initial_revenue_stage__c;
    initial_round__c;
    lsip_initial_financing_roung__c;
    lead__c;
    legal_entity_name__c;
    legal_geography__c;
    sector__c;
    short_name__c;
    x2nd_lead__c;
    documentation__c;
    company_status__c;
    region__c;
    currency_format__c;
    industry_master__c;
    sub_industry__c;
    region_country__c;
    auditor__c;
    benchmark__c;
    entity__c;
    business__c;
    year__c;
}
exports.AccountRecordDto = AccountRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the account.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Master record identifier when the account is part of a hierarchy.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "masterrecordid", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Account name.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Account type.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Parent account identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "parentid", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Primary phone number.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Website address.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "website", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Salesforce industry label.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "industry", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Account description.', maxLength: 32000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(32000),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Currency ISO code.', maxLength: 3 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 3),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "currencyisocode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Partner designation flag as received from Salesforce.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AccountRecordDto.prototype, "ispartner", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Board position held.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "board_position_held__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Company full legal name.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "company_full_name__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Going-in cost textual value from Salesforce.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => (value != null ? String(value) : null)),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "going_in_cost__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Going-in post money valuation textual value.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => (value != null ? String(value) : null)),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "going_in_post_money_value__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Going-in pre money valuation textual value.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => (value != null ? String(value) : null)),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "going_in_pre_money__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Headquarters geography.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "hq_geography__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Initial investment stage.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "initial_investment_stage__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Initial ownership textual value.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => (value != null ? String(value) : null)),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "initial_ownership__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Initial revenue stage.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "initial_revenue_stage__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Initial fundraising round.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "initial_round__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'LSIP initial financing round.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "lsip_initial_financing_roung__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'First lead identifier.', maxLength: 20 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "lead__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Legal entity name.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "legal_entity_name__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Legal geography.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "legal_geography__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Sector label.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "sector__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Short name.', maxLength: 20 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "short_name__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Second lead identifier.', maxLength: 20 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "x2nd_lead__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Documentation classification.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "documentation__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Company status.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "company_status__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Operating region.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "region__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Currency format.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "currency_format__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Industry master reference.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "industry_master__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Sub-industry reference.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "sub_industry__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Region or country label.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "region_country__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Auditor name.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "auditor__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Benchmark description.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "benchmark__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Entity descriptor.', maxLength: 4099 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4099),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "entity__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Business descriptor.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "business__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Financial Year of the account' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], AccountRecordDto.prototype, "year__c", void 0);
class UpsertAccountsDto {
    Accounts;
}
exports.UpsertAccountsDto = UpsertAccountsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [AccountRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => AccountRecordDto),
    __metadata("design:type", Array)
], UpsertAccountsDto.prototype, "Accounts", void 0);
//# sourceMappingURL=upsert-accounts.dto.js.map