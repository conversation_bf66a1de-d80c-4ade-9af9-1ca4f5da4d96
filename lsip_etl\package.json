{"name": "lsip-etl", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:run": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:run -d data-source.ts", "migration:revert": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:revert -d data-source.ts", "migration:revert-all": "ts-node -r tsconfig-paths/register src/database/revert-all-migrations.ts", "migration:show": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:show -d data-source.ts", "azure:bootstrap": "bash scripts/azure/bootstrap.sh", "azure:config": "bash scripts/azure/appsettings.sh", "azure:deploy": "bash scripts/azure/deploy.sh", "azure:showlog": "az webapp log tail --name Jarvisdev --resource-group appsvc_linux_centralindia_basic", "excel:download": "tsc src/excelController.ts --outDir dist --moduleResolution node --module commonjs --target es2020 --esModuleInterop && node dist/excelController.js", "send:cap-table": "node send-cap-table-data.js", "send:cap-table-ts": "ts-node send-cap-table-data.ts"}, "dependencies": {"@nestjs/axios": "^4.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.0.2", "@nestjs/typeorm": "^11.0.0", "@types/pdf-parse": "^1.1.5", "axios": "^1.12.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "google-auth-library": "^10.5.0", "googleapis": "^166.0.0", "pdf-parse": "^2.4.5", "pdfreader": "^3.0.8", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.27", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/express": "^5.0.0", "@types/jest": "^29.5.12", "@types/node": "^18.19.60", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "engines": {"node": ">=18.19.0 <23"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "rootDir": ".", "moduleFileExtensions": ["js", "json", "ts"], "testRegex": "src/.*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"tsconfig": "tsconfig.json"}]}, "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/**/*.module.ts", "!src/config/**", "!src/database/**", "!src/migration/**", "!src/migrations/**"], "coverageDirectory": "coverage", "coveragePathIgnorePatterns": ["/node_modules/"]}}