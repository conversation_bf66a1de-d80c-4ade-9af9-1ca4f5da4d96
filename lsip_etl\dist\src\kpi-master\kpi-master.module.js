"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiMasterModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const kpi_master_controller_1 = require("./kpi-master.controller");
const kpi_master_service_1 = require("./kpi-master.service");
const jvs_kpi_master_entity_1 = require("./jvs-kpi-master.entity");
let KpiMasterModule = class KpiMasterModule {
};
exports.KpiMasterModule = KpiMasterModule;
exports.KpiMasterModule = KpiMasterModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_kpi_master_entity_1.JvsKpiMaster])],
        controllers: [kpi_master_controller_1.KpiMasterController],
        providers: [kpi_master_service_1.KpiMasterService],
        exports: [kpi_master_service_1.KpiMasterService],
    })
], KpiMasterModule);
//# sourceMappingURL=kpi-master.module.js.map