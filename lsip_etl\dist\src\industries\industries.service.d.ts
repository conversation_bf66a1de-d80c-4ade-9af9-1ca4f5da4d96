import { Repository } from 'typeorm';
import { JvsIndustry } from './jvs-industry.entity';
import { IndustryRecord } from './dto/upsert-industries.dto';
interface UnprocessedRecord {
    id: string;
    name: string | null;
    frequency__c?: string;
    parent_industry__c?: string;
    parentIndustryAttempted: string | null;
    message: string;
}
interface UpsertIndustryResult {
    processed: number;
    unprocessedRecords: UnprocessedRecord[];
}
export declare class IndustriesService {
    private readonly repository;
    constructor(repository: Repository<JvsIndustry>);
    upsertIndustries(records: IndustryRecord[]): Promise<UpsertIndustryResult>;
}
export {};
