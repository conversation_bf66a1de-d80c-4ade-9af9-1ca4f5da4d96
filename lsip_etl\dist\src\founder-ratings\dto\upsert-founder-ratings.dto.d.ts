export declare class FounderRatingRecordDto {
    id: string;
    name?: string;
    createddate?: string;
    account__c?: string;
    vision_strategic_thinking__c?: string;
    adaptability_learnability__c?: string;
    goal_orientation__c?: string;
    org_building_ability_to_attract_high_q__c?: string;
    narrative_building_story_telling__c?: string;
    financial_prudence_burn_management__c?: string;
    pace_of_execution_bias_to_action__c?: string;
    ability_to_take_tough_decisions__c?: string;
    leading_others_outcome_from_team__c?: string;
    self_awareness__c?: string;
    integrity_transparency__c?: string;
    rating_date__c?: string;
}
export declare class UpsertFounderRatingsDto {
    FounderRatings: FounderRatingRecordDto[];
}
export type FounderRatingRecord = FounderRatingRecordDto;
