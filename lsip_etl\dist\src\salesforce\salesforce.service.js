"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SalesforceService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalesforceService = void 0;
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_2 = require("axios");
const rxjs_1 = require("rxjs");
let SalesforceService = SalesforceService_1 = class SalesforceService {
    httpService;
    configService;
    logger = new common_1.Logger(SalesforceService_1.name);
    accessToken = null;
    accessTokenExpiresAt = 0;
    instanceUrl = null;
    constructor(httpService, configService) {
        this.httpService = httpService;
        this.configService = configService;
    }
    async request(config) {
        const token = await this.ensureAccessToken();
        const requestConfig = this.buildAuthorizedConfig(config, token);
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.request(requestConfig));
            return response.data;
        }
        catch (error) {
            this.handleAxiosError(error);
        }
    }
    async ensureAccessToken() {
        if (this.accessToken && Date.now() < this.accessTokenExpiresAt) {
            return this.accessToken;
        }
        try {
            const params = new URLSearchParams({
                grant_type: 'password',
                client_id: this.configService.get('salesforce.clientId') ?? '',
                client_secret: this.configService.get('salesforce.clientSecret') ?? '',
                username: this.configService.get('salesforce.username') ?? '',
                password: `${this.configService.get('salesforce.password') ?? ''}${this.configService.get('salesforce.token') ?? ''}`,
            });
            const response = await this.httpService.axiosRef.post('/services/oauth2/token', params.toString(), {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            });
            this.accessToken = response.data.access_token;
            this.instanceUrl = response.data.instance_url ?? this.configService.get('salesforce.baseUrl');
            const ttl = this.configService.get('salesforce.tokenTtlMs') ?? 30 * 60 * 1000;
            this.accessTokenExpiresAt = Date.now() + ttl;
            return this.accessToken;
        }
        catch (error) {
            this.handleAxiosError(error);
        }
    }
    buildAuthorizedConfig(config, accessToken) {
        const headers = {
            ...(config.headers ?? {}),
            Authorization: `Bearer ${accessToken}`,
        };
        const baseURL = config.baseURL ?? this.instanceUrl ?? this.configService.get('salesforce.baseUrl') ?? undefined;
        return {
            ...config,
            baseURL,
            headers,
        };
    }
    handleAxiosError(error) {
        if (error instanceof axios_2.AxiosError) {
            const { response, message } = error;
            if (response) {
                this.logger.error(`Salesforce request failed: ${response.status} ${response.statusText} - ${JSON.stringify(response.data)}`);
                throw new Error(`Salesforce request failed with status ${response.status}: ${JSON.stringify(response.data ?? {})}`);
            }
            this.logger.error(`Salesforce request failed: ${message}`);
            throw new Error(`Salesforce request failed: ${message}`);
        }
        throw error;
    }
};
exports.SalesforceService = SalesforceService;
exports.SalesforceService = SalesforceService = SalesforceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService,
        config_1.ConfigService])
], SalesforceService);
//# sourceMappingURL=salesforce.service.js.map