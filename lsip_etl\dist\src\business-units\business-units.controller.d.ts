import { BusinessUnitsService } from './business-units.service';
import { UpsertBusinessUnitsDto } from './dto/upsert-business-units.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class BusinessUnitsController {
    private readonly businessUnitsService;
    private static readonly validationPipe;
    constructor(businessUnitsService: BusinessUnitsService);
    upsertBusinessUnits(payload: UpsertBusinessUnitsDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            companyAttempted: string | null;
            parentBusinessAttempted: string | null;
        }[];
    }>>;
}
