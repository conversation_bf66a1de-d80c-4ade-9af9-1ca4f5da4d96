import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig } from 'axios';
export declare class SalesforceService {
    private readonly httpService;
    private readonly configService;
    private readonly logger;
    private accessToken;
    private accessTokenExpiresAt;
    private instanceUrl;
    constructor(httpService: HttpService, configService: ConfigService);
    request<T = unknown>(config: AxiosRequestConfig): Promise<T>;
    private ensureAccessToken;
    private buildAuthorizedConfig;
    private handleAxiosError;
}
