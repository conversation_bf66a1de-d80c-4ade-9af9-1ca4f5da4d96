"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FounderRatingsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const founder_ratings_controller_1 = require("./founder-ratings.controller");
const founder_ratings_service_1 = require("./founder-ratings.service");
const jvs_founder_rating_entity_1 = require("./jvs-founder-rating.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
let FounderRatingsModule = class FounderRatingsModule {
};
exports.FounderRatingsModule = FounderRatingsModule;
exports.FounderRatingsModule = FounderRatingsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_founder_rating_entity_1.JvsFounderRating, jvs_account_entity_1.JvsAccount])],
        controllers: [founder_ratings_controller_1.FounderRatingsController],
        providers: [founder_ratings_service_1.FounderRatingsService],
        exports: [founder_ratings_service_1.FounderRatingsService],
    })
], FounderRatingsModule);
//# sourceMappingURL=founder-ratings.module.js.map