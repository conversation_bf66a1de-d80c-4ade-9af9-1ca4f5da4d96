import { Repository } from 'typeorm';
import { JvsKpiPerticular } from './jvs-kpi-perticular.entity';
import { KpiPerticularRecord } from './dto/upsert-kpi-perticular.dto';
import { JvsAccount } from '../accounts/jvs-account.entity';
import { JvsBusinessUnit } from '../business-units/jvs-business-unit.entity';
import { JvsKpiMaster } from '../kpi-master/jvs-kpi-master.entity';
import { JvsIndustry } from '../industries/jvs-industry.entity';
interface UnprocessedKpiPerticularRecord extends KpiPerticularRecord {
    message: string;
    accountAttempted: string | null;
    businessUnitAttempted: string | null;
    subBusinessUnitAttempted: string | null;
    kpiMasterAttempted: string | null;
    industryAttempted: string | null;
    subIndustryAttempted: string | null;
}
interface UpsertKpiPerticularResult {
    processed: number;
    unprocessedRecords: UnprocessedKpiPerticularRecord[];
}
export declare class KpiPerticularService {
    private readonly kpiPerticularRepository;
    private readonly accountRepository;
    private readonly businessUnitRepository;
    private readonly kpiMasterRepository;
    private readonly industryRepository;
    constructor(kpiPerticularRepository: Repository<JvsKpiPerticular>, accountRepository: Repository<JvsAccount>, businessUnitRepository: Repository<JvsBusinessUnit>, kpiMasterRepository: Repository<JvsKpiMaster>, industryRepository: Repository<JvsIndustry>);
    upsertKpiPerticulars(records: KpiPerticularRecord[]): Promise<UpsertKpiPerticularResult>;
}
export {};
