export declare class AccountRecordDto {
    id: string;
    masterrecordid?: string;
    name?: string;
    type?: string;
    parentid?: string;
    phone?: string;
    website?: string;
    industry?: string;
    description?: string;
    currencyisocode?: string;
    ispartner?: boolean;
    board_position_held__c?: string;
    company_full_name__c?: string;
    going_in_cost__c?: string;
    going_in_post_money_value__c?: string;
    going_in_pre_money__c?: string;
    hq_geography__c?: string;
    initial_investment_stage__c?: string;
    initial_ownership__c?: string;
    initial_revenue_stage__c?: string;
    initial_round__c?: string;
    lsip_initial_financing_roung__c?: string;
    lead__c?: string;
    legal_entity_name__c?: string;
    legal_geography__c?: string;
    sector__c?: string;
    short_name__c?: string;
    x2nd_lead__c?: string;
    documentation__c?: string;
    company_status__c?: string;
    region__c?: string;
    currency_format__c?: string;
    industry_master__c?: string;
    sub_industry__c?: string;
    region_country__c?: string;
    auditor__c?: string;
    benchmark__c?: string;
    entity__c?: string;
    business__c?: string;
    year__c?: string;
}
export declare class UpsertAccountsDto {
    Accounts: AccountRecordDto[];
}
export type AccountRecord = AccountRecordDto;
