"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertCompanyFundsDto = exports.CompanyFundRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CompanyFundRecordDto {
    id;
    isdeleted;
    name;
    currencyisocode;
    createddate;
    createdbyid;
    lastmodifieddate;
    lastmodifiedbyid;
    systemmodstamp;
    lastactivitydate;
    fund__c;
    account__c;
    board_member__c;
    going_in_cost__c;
    going_in_post_money_value_m__c;
    going_in_pre_money_value_m__c;
    initial_date_of_investment__c;
    initial_investment_stage__c;
    initial_ownership__c;
    initial_revenue_stage__c;
    initial_round__c;
    lead_primary__c;
    lead_secondary__c;
    short_name__c;
    key__c;
}
exports.CompanyFundRecordDto = CompanyFundRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the company fund record.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Marks the Salesforce record as deleted.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CompanyFundRecordDto.prototype, "isdeleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Company fund name.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Currency ISO code.', maxLength: 3 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 3),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "currencyisocode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Original Salesforce created date payload.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "createddate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'User that created the record.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "createdbyid", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Timestamp of last modification.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "lastmodifieddate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'User that last modified the record.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "lastmodifiedbyid", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'System mod stamp string as provided by Salesforce.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "systemmodstamp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Last activity date.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "lastactivitydate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Fund reference.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "fund__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Account reference.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "account__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Board member contact reference.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "board_member__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Going-in cost value.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CompanyFundRecordDto.prototype, "going_in_cost__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Going-in post-money value (millions).' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CompanyFundRecordDto.prototype, "going_in_post_money_value_m__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Going-in pre-money value (millions).' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CompanyFundRecordDto.prototype, "going_in_pre_money_value_m__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Initial date of investment.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "initial_date_of_investment__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Initial investment stage.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "initial_investment_stage__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Initial ownership percentage.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CompanyFundRecordDto.prototype, "initial_ownership__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Initial revenue stage.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "initial_revenue_stage__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Initial round.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "initial_round__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Primary lead reference.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "lead_primary__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Secondary lead reference.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "lead_secondary__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Short name.', maxLength: 50 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "short_name__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Key identifier.', maxLength: 100 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CompanyFundRecordDto.prototype, "key__c", void 0);
class UpsertCompanyFundsDto {
    CompanyFunds;
}
exports.UpsertCompanyFundsDto = UpsertCompanyFundsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [CompanyFundRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CompanyFundRecordDto),
    __metadata("design:type", Array)
], UpsertCompanyFundsDto.prototype, "CompanyFunds", void 0);
//# sourceMappingURL=upsert-company-funds.dto.js.map