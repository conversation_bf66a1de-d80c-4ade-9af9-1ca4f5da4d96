"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiDetailsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const kpi_details_service_1 = require("./kpi-details.service");
const upsert_kpi_details_dto_1 = require("./dto/upsert-kpi-details.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const kpi_details_response_dto_1 = require("./dto/kpi-details-response.dto");
let KpiDetailsController = class KpiDetailsController {
    kpiDetailsService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(kpiDetailsService) {
        this.kpiDetailsService = kpiDetailsService;
    }
    async upsertKpiDetails(payload) {
        const result = await this.kpiDetailsService.upsertKpiDetails(payload.KpiDetails);
        return (0, api_response_dto_1.createSuccessResponse)('KPI detail records processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.KpiDetailsController = KpiDetailsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(KpiDetailsController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert KPI detail records.' }),
    (0, swagger_1.ApiBody)({ type: upsert_kpi_details_dto_1.UpsertKpiDetailsDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'KPI detail records processed successfully.',
        type: kpi_details_response_dto_1.KpiDetailsApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_kpi_details_dto_1.UpsertKpiDetailsDto]),
    __metadata("design:returntype", Promise)
], KpiDetailsController.prototype, "upsertKpiDetails", null);
exports.KpiDetailsController = KpiDetailsController = __decorate([
    (0, swagger_1.ApiTags)('KPI Details'),
    (0, common_1.Controller)('kpi-details'),
    __metadata("design:paramtypes", [kpi_details_service_1.KpiDetailsService])
], KpiDetailsController);
//# sourceMappingURL=kpi-details.controller.js.map