import { KpiPerticularService } from './kpi-perticular.service';
import { UpsertKpiPerticularDto } from './dto/upsert-kpi-perticular.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class KpiPerticularController {
    private readonly kpiPerticularService;
    private static readonly validationPipe;
    constructor(kpiPerticularService: KpiPerticularService);
    upsertKpiPerticulars(payload: UpsertKpiPerticularDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            accountAttempted: string | null;
            businessUnitAttempted: string | null;
            subBusinessUnitAttempted: string | null;
            kpiMasterAttempted: string | null;
            industryAttempted: string | null;
            subIndustryAttempted: string | null;
        }[];
    }>>;
}
