"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertContactsDto = exports.ContactRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class ContactRecordDto {
    id;
    isdeleted;
    masterrecordid;
    accountid;
    lastname;
    firstname;
    salutation;
    middlename;
    name;
    phone;
    fax;
    mobilephone;
    assistantphone;
    reportstoid;
    email;
    title;
    department;
    assistantname;
    birthdate;
    hasoptedoutofemail;
    hasoptedoutoffax;
    donotcall;
    titletype;
    departmentgroup;
    company_fund__c;
    dayofmonth__c;
    designation_rank__c;
    dob__c;
    director__c;
    department_2__c;
    designation__c;
    key_contact__c;
    rating__c;
}
exports.ContactRecordDto = ContactRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the contact record.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Marks the Salesforce record as deleted.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ContactRecordDto.prototype, "isdeleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Master record identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "masterrecordid", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Account identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "accountid", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Last name.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "lastname", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'First name.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "firstname", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Salutation.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "salutation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Middle name.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "middlename", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Full name.', maxLength: 121 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(121),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Primary phone number.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Fax number.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "fax", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Mobile phone number.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "mobilephone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Assistant phone number.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "assistantphone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Contact identifier this record reports to.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "reportstoid", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Primary email address.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Job title.', maxLength: 128 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(128),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Department.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "department", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Assistant name.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "assistantname", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Birthdate.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "birthdate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Email opt-out flag.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ContactRecordDto.prototype, "hasoptedoutofemail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Fax opt-out flag.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ContactRecordDto.prototype, "hasoptedoutoffax", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Do not call flag.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ContactRecordDto.prototype, "donotcall", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Title type.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "titletype", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Department group.', maxLength: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(40),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "departmentgroup", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Company fund identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "company_fund__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Day of month.', example: 15 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ContactRecordDto.prototype, "dayofmonth__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Designation rank.', example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ContactRecordDto.prototype, "designation_rank__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Date of birth.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "dob__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Director flag.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ContactRecordDto.prototype, "director__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Additional department.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "department_2__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Designation.', maxLength: 100 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "designation__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Key contact flag.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ContactRecordDto.prototype, "key_contact__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Rating.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], ContactRecordDto.prototype, "rating__c", void 0);
class UpsertContactsDto {
    Contacts;
}
exports.UpsertContactsDto = UpsertContactsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [ContactRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => ContactRecordDto),
    __metadata("design:type", Array)
], UpsertContactsDto.prototype, "Contacts", void 0);
//# sourceMappingURL=upsert-contacts.dto.js.map