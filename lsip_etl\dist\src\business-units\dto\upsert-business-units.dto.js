"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertBusinessUnitsDto = exports.BusinessUnitRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class BusinessUnitRecordDto {
    id;
    name;
    company__c;
    parent_business__c;
    active__c;
    benchmark__c;
    type__c;
}
exports.BusinessUnitRecordDto = BusinessUnitRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the business unit.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], BusinessUnitRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Business unit name.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], BusinessUnitRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Associated account identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], BusinessUnitRecordDto.prototype, "company__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Parent business unit identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], BusinessUnitRecordDto.prototype, "parent_business__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Flag indicating whether the unit is active.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], BusinessUnitRecordDto.prototype, "active__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Benchmark reference.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], BusinessUnitRecordDto.prototype, "benchmark__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Business unit type.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessUnitRecordDto.prototype, "type__c", void 0);
class UpsertBusinessUnitsDto {
    BusinessUnits;
}
exports.UpsertBusinessUnitsDto = UpsertBusinessUnitsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [BusinessUnitRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => BusinessUnitRecordDto),
    __metadata("design:type", Array)
], UpsertBusinessUnitsDto.prototype, "BusinessUnits", void 0);
//# sourceMappingURL=upsert-business-units.dto.js.map