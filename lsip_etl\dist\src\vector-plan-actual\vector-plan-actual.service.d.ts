import { Repository } from 'typeorm';
import { JvsVectorPlanActual } from './jvs-vector-plan-actual.entity';
import { VectorPlanActualRecord } from './dto/upsert-vector-plan-actual.dto';
import { JvsVectorDetail } from '../vector-detail/jvs-vector-detail.entity';
import { JvsKpiPlanActual } from '../kpi-plan-actual/jvs-kpi-plan-actual.entity';
interface UnprocessedVectorPlanActualRecord extends VectorPlanActualRecord {
    message: string;
    vectorDetailAttempted: string | null;
    kpiPlanActualAttempted: string | null;
}
interface UpsertVectorPlanActualResult {
    processed: number;
    unprocessedRecords: UnprocessedVectorPlanActualRecord[];
}
export declare class VectorPlanActualService {
    private readonly vectorPlanActualRepository;
    private readonly vectorDetailRepository;
    private readonly kpiPlanActualRepository;
    constructor(vectorPlanActualRepository: Repository<JvsVectorPlanActual>, vectorDetailRepository: Repository<JvsVectorDetail>, kpiPlanActualRepository: Repository<JvsKpiPlanActual>);
    upsertVectorPlanActuals(records: VectorPlanActualRecord[]): Promise<UpsertVectorPlanActualResult>;
}
export {};
