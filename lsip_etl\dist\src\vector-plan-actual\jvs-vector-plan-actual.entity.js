"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsVectorPlanActual = void 0;
const typeorm_1 = require("typeorm");
let JvsVectorPlanActual = class JvsVectorPlanActual {
    id;
    name = null;
    currencyIsoCode = null;
    actual = null;
    month1 = null;
    alternateName = null;
    numberOfPlanRevisions = null;
    plan = null;
    quarter = null;
    reasonForDeviationRemarks = null;
    uniqueIdentifier = null;
    vectorDetailId = null;
    year = null;
    monthNumber = null;
    consolidatedDate = null;
    kpiPlanActualId = null;
    planActualDate = null;
};
exports.JvsVectorPlanActual = JvsVectorPlanActual;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsVectorPlanActual.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'currencyisocode', type: 'varchar', length: 3, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "currencyIsoCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'actual__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "actual", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'month1__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "month1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'name__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "alternateName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_plan_revisions__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "numberOfPlanRevisions", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'plan__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "plan", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'quarter__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "quarter", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reason_for_deviation_remarks__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "reasonForDeviationRemarks", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'unique_identifier__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "uniqueIdentifier", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vector_detail__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "vectorDetailId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'year__c', type: 'varchar', length: 4, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "year", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'month_number__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "monthNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'consolidated_date__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "consolidatedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kpi_plan_actual__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "kpiPlanActualId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'plan_actual_date__c', type: 'date', nullable: true }),
    __metadata("design:type", Object)
], JvsVectorPlanActual.prototype, "planActualDate", void 0);
exports.JvsVectorPlanActual = JvsVectorPlanActual = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_vector_plan_actual' })
], JvsVectorPlanActual);
//# sourceMappingURL=jvs-vector-plan-actual.entity.js.map