"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiRelevancyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_kpi_relevancy_entity_1 = require("./jvs-kpi-relevancy.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
const jvs_kpi_master_entity_1 = require("../kpi-master/jvs-kpi-master.entity");
const jvs_industry_entity_1 = require("../industries/jvs-industry.entity");
let KpiRelevancyService = class KpiRelevancyService {
    kpiRelevancyRepository;
    accountRepository;
    kpiMasterRepository;
    industryRepository;
    constructor(kpiRelevancyRepository, accountRepository, kpiMasterRepository, industryRepository) {
        this.kpiRelevancyRepository = kpiRelevancyRepository;
        this.accountRepository = accountRepository;
        this.kpiMasterRepository = kpiMasterRepository;
        this.industryRepository = industryRepository;
    }
    async upsertKpiRelevancies(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedCompanyIds = new Set(records.map((record) => record.companyId).filter((value) => Boolean(value)));
        const requestedKpiMasterIds = new Set(records.map((record) => record.kpiMasterId).filter((value) => Boolean(value)));
        const requestedIndustryIds = new Set(records
            .flatMap((record) => [record.industryMasterId, record.subIndustryId])
            .filter((value) => Boolean(value)));
        const existingCompanyIds = requestedCompanyIds.size
            ? new Set((await this.accountRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedCompanyIds]) },
            })).map((account) => account.id))
            : new Set();
        const existingKpiMasterIds = requestedKpiMasterIds.size
            ? new Set((await this.kpiMasterRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedKpiMasterIds]) },
            })).map((kpiMaster) => kpiMaster.id))
            : new Set();
        const existingIndustryIds = requestedIndustryIds.size
            ? new Set((await this.industryRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedIndustryIds]) },
            })).map((industry) => industry.id))
            : new Set();
        const entities = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                companyAttempted: null,
                industryMasterAttempted: null,
                kpiMasterAttempted: null,
                subIndustryAttempted: null,
            };
            const companyId = record.companyId && existingCompanyIds.has(record.companyId) ? record.companyId : null;
            if (record.companyId && !companyId) {
                meta.companyAttempted = record.companyId;
                meta.issues.push(`Account ${record.companyId} not found; stored without company linkage.`);
            }
            const kpiMasterId = record.kpiMasterId && existingKpiMasterIds.has(record.kpiMasterId) ? record.kpiMasterId : null;
            if (record.kpiMasterId && !kpiMasterId) {
                meta.kpiMasterAttempted = record.kpiMasterId;
                meta.issues.push(`KPI master ${record.kpiMasterId} not found; stored without KPI master linkage.`);
            }
            const industryMasterId = record.industryMasterId && existingIndustryIds.has(record.industryMasterId)
                ? record.industryMasterId
                : null;
            if (record.industryMasterId && !industryMasterId) {
                meta.industryMasterAttempted = record.industryMasterId;
                meta.issues.push(`Industry master ${record.industryMasterId} not found; stored without industry linkage.`);
            }
            const subIndustryId = record.subIndustryId && existingIndustryIds.has(record.subIndustryId) ? record.subIndustryId : null;
            if (record.subIndustryId && !subIndustryId) {
                meta.subIndustryAttempted = record.subIndustryId;
                meta.issues.push(`Sub-industry ${record.subIndustryId} not found; stored without sub-industry linkage.`);
            }
            const entity = this.kpiRelevancyRepository.create({
                ...record,
                companyId,
                kpiMasterId,
                industryMasterId,
                subIndustryId,
            });
            metaById.set(record.id, meta);
            entities.push(entity);
        }
        if (entities.length > 0) {
            await this.kpiRelevancyRepository.save(entities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    companyAttempted: meta.companyAttempted,
                    industryMasterAttempted: meta.industryMasterAttempted,
                    kpiMasterAttempted: meta.kpiMasterAttempted,
                    subIndustryAttempted: meta.subIndustryAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.KpiRelevancyService = KpiRelevancyService;
exports.KpiRelevancyService = KpiRelevancyService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_kpi_relevancy_entity_1.JvsKpiRelevancy)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __param(2, (0, typeorm_1.InjectRepository)(jvs_kpi_master_entity_1.JvsKpiMaster)),
    __param(3, (0, typeorm_1.InjectRepository)(jvs_industry_entity_1.JvsIndustry)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], KpiRelevancyService);
//# sourceMappingURL=kpi-relevancy.service.js.map