"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountsApiResponseDto = exports.AccountsProcessedDataDto = exports.UnprocessedAccountRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_accounts_dto_1 = require("./upsert-accounts.dto");
class UnprocessedAccountRecordDto extends upsert_accounts_dto_1.AccountRecordDto {
    message;
    parentAccountAttempted;
    industryMasterAttempted;
    subIndustryAttempted;
}
exports.UnprocessedAccountRecordDto = UnprocessedAccountRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedAccountRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Parent account identifier received in the payload.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedAccountRecordDto.prototype, "parentAccountAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Industry master identifier received in the payload.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedAccountRecordDto.prototype, "industryMasterAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Sub-industry identifier received in the payload.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedAccountRecordDto.prototype, "subIndustryAttempted", void 0);
class AccountsProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.AccountsProcessedDataDto = AccountsProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of account records processed.' }),
    __metadata("design:type", Number)
], AccountsProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings (e.g., unresolved relationships).',
        type: () => [UnprocessedAccountRecordDto],
    }),
    __metadata("design:type", Array)
], AccountsProcessedDataDto.prototype, "unprocessedRecords", void 0);
class AccountsApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.AccountsApiResponseDto = AccountsApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: AccountsProcessedDataDto }),
    __metadata("design:type", AccountsProcessedDataDto)
], AccountsApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=accounts-response.dto.js.map