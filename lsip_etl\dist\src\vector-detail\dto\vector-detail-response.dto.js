"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorDetailApiResponseDto = exports.VectorDetailProcessedDataDto = exports.UnprocessedVectorDetailRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_vector_detail_dto_1 = require("./upsert-vector-detail.dto");
class UnprocessedVectorDetailRecordDto extends upsert_vector_detail_dto_1.VectorDetailRecordDto {
    message;
    kpiPerticularAttempted;
    vectorMasterAttempted;
}
exports.UnprocessedVectorDetailRecordDto = UnprocessedVectorDetailRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedVectorDetailRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KPI particular identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedVectorDetailRecordDto.prototype, "kpiPerticularAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Vector master identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedVectorDetailRecordDto.prototype, "vectorMasterAttempted", void 0);
class VectorDetailProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.VectorDetailProcessedDataDto = VectorDetailProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], VectorDetailProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings.',
        type: () => [UnprocessedVectorDetailRecordDto],
    }),
    __metadata("design:type", Array)
], VectorDetailProcessedDataDto.prototype, "unprocessedRecords", void 0);
class VectorDetailApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.VectorDetailApiResponseDto = VectorDetailApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: VectorDetailProcessedDataDto }),
    __metadata("design:type", VectorDetailProcessedDataDto)
], VectorDetailApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=vector-detail-response.dto.js.map