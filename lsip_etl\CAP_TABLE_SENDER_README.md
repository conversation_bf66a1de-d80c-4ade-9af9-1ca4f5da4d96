# Cap Table Data Sender

This document explains how to send your Cap Table JSON data to the company-funds endpoint without using Postman.

## Files Created

1. **`send-cap-table-data.js`** - JavaScript version of the sender script
2. **`send-cap-table-data.ts`** - TypeScript version of the sender script
3. **Updated `package.json`** - Added npm scripts for easy execution

## How to Use

### Prerequisites

1. Make sure your NestJS server is running:
   ```bash
   npm run start:dev
   ```
   
2. Ensure your JSON file exists at: `src/ExcelJsonData/Cap table.json`

### Method 1: Using npm scripts (Recommended)

```bash
# Using JavaScript version
npm run send:cap-table

# Using TypeScript version  
npm run send:cap-table-ts
```

### Method 2: Direct execution

```bash
# JavaScript version
node send-cap-table-data.js

# TypeScript version (requires ts-node)
npx ts-node send-cap-table-data.ts
```

## What the Script Does

1. **Reads** your JSON file from `src/ExcelJsonData/Cap table.json`
2. **Validates** that the file exists and can be parsed
3. **Sends** a POST request to `http://localhost:3000/company-funds`
4. **Displays** the response from your server

## Expected Output

```
Reading JSON file: /path/to/your/src/ExcelJsonData/Cap table.json
Loaded data: {
  "CompanyFunds": [
    {
      "id": "derererere",
      "isdeleted": true,
      "name": "Series A Investment",
      ...
    }
  ]
}
Found 1 company fund records

Sending POST request to: http://localhost:3000/company-funds
Request options: { hostname: 'localhost', port: '3000', path: '/company-funds', method: 'POST', ... }

✅ Success! Response received:
Status: 200
Headers: { ... }
Body: {
  "success": true,
  "message": "Company funds processed successfully.",
  "data": {
    "processed": 1,
    "unprocessedRecords": []
  }
}
```

## Configuration

You can modify the following constants in the script files if needed:

- **`SERVER_URL`**: Change if your server runs on a different port (default: `http://localhost:3000`)
- **`ENDPOINT`**: The API endpoint path (default: `/company-funds`)
- **`JSON_FILE_PATH`**: Path to your JSON file (default: `src/ExcelJsonData/Cap table.json`)

## Troubleshooting

### Error: "JSON file not found"
- Check that the file exists at `src/ExcelJsonData/Cap table.json`
- Verify the file path is correct

### Error: "ECONNREFUSED"
- Make sure your NestJS server is running with `npm run start:dev`
- Check that the server is running on the correct port (default: 3000)

### Error: "Validation failed"
- Check that your JSON data matches the expected DTO structure
- Ensure required fields like `id` are present and valid

## JSON Data Structure

Your JSON file should match this structure:

```json
{
  "CompanyFunds": [
    {
      "id": "string (required, max 18 chars)",
      "isdeleted": "boolean (optional)",
      "name": "string (optional, max 80 chars)",
      "currencyisocode": "string (optional, max 3 chars)",
      // ... other optional fields
    }
  ]
}
```
