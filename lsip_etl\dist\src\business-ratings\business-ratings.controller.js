"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessRatingsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const business_ratings_service_1 = require("./business-ratings.service");
const upsert_business_ratings_dto_1 = require("./dto/upsert-business-ratings.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const business_ratings_response_dto_1 = require("./dto/business-ratings-response.dto");
let BusinessRatingsController = class BusinessRatingsController {
    businessRatingsService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(businessRatingsService) {
        this.businessRatingsService = businessRatingsService;
    }
    async upsertBusinessRatings(payload) {
        const result = await this.businessRatingsService.upsertBusinessRatings(payload.BusinessRatings);
        return (0, api_response_dto_1.createSuccessResponse)('Business ratings processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.BusinessRatingsController = BusinessRatingsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(BusinessRatingsController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert business rating data.' }),
    (0, swagger_1.ApiBody)({ type: upsert_business_ratings_dto_1.UpsertBusinessRatingsDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Business ratings processed successfully.',
        type: business_ratings_response_dto_1.BusinessRatingsApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_business_ratings_dto_1.UpsertBusinessRatingsDto]),
    __metadata("design:returntype", Promise)
], BusinessRatingsController.prototype, "upsertBusinessRatings", null);
exports.BusinessRatingsController = BusinessRatingsController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('business-ratings'),
    __metadata("design:paramtypes", [business_ratings_service_1.BusinessRatingsService])
], BusinessRatingsController);
//# sourceMappingURL=business-ratings.controller.js.map