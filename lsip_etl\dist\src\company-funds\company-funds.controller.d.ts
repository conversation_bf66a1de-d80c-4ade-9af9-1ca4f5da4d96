import { CompanyFundsService } from './company-funds.service';
import { UpsertCompanyFundsDto } from './dto/upsert-company-funds.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class CompanyFundsController {
    private readonly companyFundsService;
    private static readonly validationPipe;
    constructor(companyFundsService: CompanyFundsService);
    upsertCompanyFunds(payload: UpsertCompanyFundsDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            accountAttempted: string | null;
        }[];
    }>>;
}
