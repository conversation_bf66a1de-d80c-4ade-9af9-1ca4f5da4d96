export declare class KpiDetailRecordDto {
    id: string;
    name?: string;
    currencyIsoCode?: string;
    companyId?: string;
    aovRs?: number;
    available?: boolean;
    averageOrderValueProduct?: number;
    averageOrderValue?: number;
    businessUnitId?: string;
    cacRs?: number;
    cac?: number;
    cm2?: number;
    cm3OfNmv?: number;
    cmInLocalitiesWith500Users?: number;
    cumulativeBankAccounts?: number;
    bankAccountsCreatedPerMonth?: number;
    cashInBank?: number;
    categoryId?: string;
    contributionMarginProduct?: number;
    averageDepositBankAccount?: number;
    contributionMargin?: number;
    numberOfCreditCardIssued?: number;
    customerAcquisitonCost?: number;
    dau?: number;
    dtrFromSource?: number;
    date?: string;
    cashBurnRsCr?: number;
    cashBurn?: number;
    ebitdaRsCr?: number;
    ebitda?: number;
    enable?: boolean;
    firstPartyMix?: number;
    firstParty?: number;
    fulfillmentCostOfNmv?: number;
    gmv?: number;
    gmvFromPrivateLabels?: number;
    gmvPerExistingBuyer?: number;
    gmvPerFosCost?: number;
    gmvPerNewBuyer?: number;
    contributionMarginRsCr?: number;
    grossMargin?: number;
    grossMarginOfNmv?: number;
    inventoryDays?: number;
    latestFlag?: boolean;
    m12GmvRetention?: number;
    m12UserRetention?: number;
    m1Repeat?: number;
    m2GmvRetention?: number;
    contributionMarginOfTakeRate?: number;
    m2UserRetention?: number;
    m6GmvRetention?: number;
    m6UserRetention?: number;
    discretionaryMarketing?: number;
    grossMarginProduct?: number;
    mau?: number;
    marketingSpends?: number;
    m2Retention?: number;
    marketplaceFloat?: number;
    merchantAcquisitionCost?: number;
    nmv?: number;
    nmvPerBuyerRs?: number;
    noOfActiveTrucks?: number;
    noOfDenseLanes?: number;
    noOfDenseLocalities?: number;
    noOfOrders?: number;
    noOfPayingMerchants?: number;
    noOfTrackedTrips?: number;
    noOfTransactingBuyers?: number;
    noOfTransactingUsers?: number;
    noOfTransactions?: number;
    macRs?: number;
    numberOfBuyersNumber?: number;
    numberOfLocalitiesWith500Users?: number;
    numberOfNewUsers?: number;
    numberOfTransactingUsersLakh?: number;
    operatingCashBurnRsCr?: number;
    orderFrequency?: number;
    payableDays?: number;
    payingMerchants?: number;
    peopleGAOthersRsCr?: number;
    mauLakh?: number;
    marketing?: number;
    receivableDays?: number;
    recordTypeName?: string;
    revenuetotal?: number;
    salesCost?: number;
    shippingCost?: number;
    shippingCostOfNmv?: number;
    takeRateGmv?: number;
    takeRateRsCr?: number;
    takeRate?: number;
    takeRateByRevenueProduct?: number;
    takeRateByRevenue?: number;
    takeRateFromLocalitiesWith500User?: number;
    takeRatePerUserRs?: number;
    takeRateRevenueFromDenseLocalitie?: number;
    takeRateRevenuePerExistingUser?: number;
    takeRateRevenuePerNewUser?: number;
    testKpi?: number;
    totalPeopleCost?: number;
    transactingBuyers?: number;
    transactingStoresPerFos?: number;
    transactingUserInLocalitiesWith500?: number;
    typeOfKpi?: string;
    workingCapital?: number;
    key?: string;
    numberOfBuyers?: number;
    revenueRsCr?: number;
    cm1?: number;
    customerSupportBizOpsCost?: number;
    underUtilizedFixedCostsInOperations?: number;
    techProductCosts?: number;
    sgA?: number;
    buyerWithCredit?: number;
    yim12MonthsAfterDpd?: number;
    performanceMarketing?: number;
    cmRsCr?: number;
    takeRateOfLocalitiesWith500Users?: number;
    performanceMarketingRsCr?: number;
    cmInLocalities500UsersRsCr?: number;
    payingUsers?: number;
    m3PayingUserRetention?: number;
    m3PayingRevenueRetention?: number;
    numberOfAudioHeardPerDau?: number;
    moArpu?: number;
    totalArpu?: number;
    cacDollar?: number;
    m0Conversion?: number;
    conversion?: number;
    subscritpionIncome?: number;
    marketingCost?: number;
    employeeExpenseIndiaUs?: number;
    ebitdaDollar?: number;
    totalCashburn?: number;
    nmvUsd?: number;
    fmcgNmv?: number;
    temanUlaNmv?: number;
    othersNmv?: number;
    takeRateNetMargin?: number;
    takeRateFmcg?: number;
    takeRateTermanUla?: number;
    logisticsCost?: number;
    fos?: number;
    warehouse?: number;
    cashInBankDollar?: number;
    uniqueOrderingStores?: number;
    m6StoreRetention?: number;
    nmvPerStorePerMonth?: number;
    aovDollar?: number;
    ordersPerStorePerMonth?: number;
    appRunningCostServerAnalyticsCos?: number;
    marketingCostPercent?: number;
    contentServingCost?: number;
    otherBrandingExpenses?: number;
    musicLicenseCost?: number;
    employeeBenefitsExpenses?: number;
    others?: number;
    dauMau?: number;
    retentionD30?: number;
    averageTimeSpent?: number;
    uniqueCreators?: number;
    totalInstalls?: number;
    organic?: number;
    costPerInstall?: number;
    revenueDollar?: number;
    skilling?: number;
    recruitment?: number;
    marketingOthers?: number;
    salary?: number;
    newInstalls?: number;
    profileComplete?: number;
    uninstallProfileCompletes?: number;
    screenedLeads?: number;
    fromExisting?: number;
    fromReactivated?: number;
    fromNew?: number;
    screenedLeadsMau?: number;
    m3UserRetention?: number;
    repeatMauToLifeToDateUserBase?: number;
    blendedCacForUser?: number;
    newEmployerAcquired?: number;
    activeEmployer?: number;
    jobsActivated?: number;
    m3EmployerRetention?: number;
    repeatEmployerToLifeToDateEmployer?: number;
    blendedCacEmployer?: number;
    communityMauOfAppMau?: number;
    communityToCommunityM1D30D60Rete?: number;
    overallEnquiriesReceivedMtdYtd?: number;
    conversionRateTrailing3Months?: number;
    newOrdersForTheMonth?: number;
    gtvArrOnSaas?: number;
    gmvDollar?: number;
    takeRateDollar?: number;
    deliveredGmvForTheMonth?: number;
    cumulativeOpenOrderBookClosing?: number;
    cmPercent?: number;
    numberOfLspsShippersAdded?: number;
    activeShippersLsps?: number;
    fixedExpenses?: number;
    gmvFromRepeatCustomers?: number;
    newCustomersAcquired?: number;
    activeCustomers?: number;
    q3CustomerRetention?: number;
    activeSuppliers?: number;
    q3SupplierRetention?: number;
    arValueInRsCrs?: number;
    apValueInRsCrs?: number;
    inventoryValueInRsCrs?: number;
    netWcArApInvAdv?: number;
    wcConversionDays?: number;
    freeCash?: number;
    endingCarr?: number;
    newCarrUpsell?: number;
    monthlyChurnDowngrade?: number;
    noOfCustomersCarr?: number;
    carrPerCustomer?: number;
    endingArr?: number;
    newArrUpsell?: number;
    monthlyChurnDowngradeArr?: number;
    numberOfCustomersArr?: number;
    arrPerCustomer?: number;
    arrAsAOfCarr?: number;
    platform?: number;
    cpaas?: number;
    voice?: number;
    services?: number;
    grossProfit?: number;
    operatingIncome?: number;
    dso?: number;
    netRetention?: number;
    averageCarrPerNewCustomer?: number;
    carrFte?: number;
    arrFte?: number;
    annualisedNetChurn?: number;
    ruleOf40?: number;
    netMagicNumber?: number;
    salesCost000?: number;
    netRevenue?: number;
    otherRevenueEGShippingRevenueEtc?: number;
    cogs?: number;
    allDiscountsRefundsEtc?: number;
    wastage?: number;
    parkingFuelLastMile?: number;
    packagingCost?: number;
    hubOperationsCost?: number;
    cm2PercentageOfNetrevenue?: number;
    marketingCostPercentageOfNetrevenue?: number;
    cm3PercentOfNetRevenue?: number;
    numberOfOrders?: number;
    existingBuyers?: number;
    ordersPerBuyer?: number;
    existingBuyersNoBuyerPermonth?: number;
    noOfUniqueBuyers?: number;
    existingBuyers000?: number;
    gmvFromExistingBuyers?: number;
    m3GmvRetention?: number;
    gmvFromTop3Categories?: number;
    grossBooking?: number;
    newExpansion?: number;
    downgradeChurn?: number;
    ndr?: number;
    salesMarketing?: number;
    generalAdministrative?: number;
    researchDevelopment?: number;
    netBurn000?: number;
    burnMultiple?: number;
    avgNewDealSize?: number;
    eventCreated?: number;
    eventHosted?: number;
    eventsWithLessThan500Participants?: number;
    eventsWith500To1000Participants?: number;
    eventsWithGreater1000Participants?: number;
    roiOnMarketingSpends?: number;
    roiOnSalesSpends?: number;
    salesQuotaAttainment?: number;
    peopleRelatedSpend?: number;
    cashBurnDollar000?: number;
    netDollarRetention?: number;
    grossRetention?: number;
    netSeatRetention?: number;
    grossSeatRetention?: number;
    logoRetention?: number;
    totalSMSpend?: number;
    sMEfficiency?: number;
    newLogoArr?: number;
    newLogoSeats?: number;
    newLogo?: number;
    newLogoAsp?: number;
    newLogoAvgSeats?: number;
    ebitdaBurn?: number;
    totalBookings?: number;
    global?: number;
    smb?: number;
    mm?: number;
    enterprise?: number;
    invoicedRevenue?: number;
    gm?: number;
    smbPercent?: number;
    mmPercent?: number;
    enterprisePercent?: number;
    mlToMql?: number;
    mqlToSql?: number;
    sqlToClosed?: number;
    grossRoi?: number;
    outboundQuotaAttainment?: number;
    enterpriseMmCac?: number;
    smbInboundMarketingCac?: number;
    saasLogoQ1Retention?: number;
    saasRevenueQ1Retention?: number;
    saasConversionSelfServeFunnel?: number;
    marketplaceEnterpriseLogoQ1Reten?: number;
    marketplaceEnterpriseRevenueQ1Re?: number;
    marketplaceMmLogoQ1Retention?: number;
    marketplaceMmRevenueQ1Retention?: number;
    creatorEarnings?: number;
    editorEarnings?: number;
    cm1Percent?: number;
    inboundQuotaAttainment?: number;
    m3SpendPerCustomer?: number;
    m3M0SpendPerCustomer?: number;
    npas?: number;
    moneyRemittedToIndiaOnZolve?: number;
    discretionaryMarketingRsCr?: number;
    takeRateOthers?: number;
    subsRevenue?: number;
    subsHostingCost?: number;
    subsClientSupportCost?: number;
    arrCarr?: number;
    iarrContracted?: number;
    employeesOnPlatformContracted?: number;
    averageAcv?: number;
    tradingVolume?: number;
    tradingRevenue?: number;
    cumulativeDownloads?: number;
    cumulativeKycedUsers?: number;
    numberOfActiveTraders?: number;
    traderW24Retention?: number;
    averageTradingFrequencyPerTrader?: number;
    totalContractsValueArr?: number;
    totalTerminalArr?: number;
    terminalArrNetOfDiscounts?: number;
    terminalArrAdded?: number;
    terminalArrLost?: number;
    nonRecurringRevenue?: number;
    totalInstitutes?: number;
    numberOfNewInstitutes?: number;
    terminalArrInstitute?: number;
    sMEfficiencyNetOfDiscounts?: number;
    totalCreators?: number;
    numberOfNewCreators?: number;
    totalCustomers?: number;
    terminalArrLostDollar?: number;
    q2TripRetention?: number;
    m6LspRetenAdjustedForPilotTrail?: number;
    m6ShipperRetentionAdjustedTrials?: number;
    numberOfSuppliersAdded?: number;
    aumTotal?: number;
    grossProfitPercent?: number;
    aumCash?: number;
    aumCpf?: number;
    aumSrs?: number;
    newInvestment?: number;
    redemption?: number;
    netNewMoneyInvested?: number;
    existingClients?: number;
    newClients?: number;
    registeredAccounts?: number;
    brokerAccounts?: number;
    fundedClients?: number;
    churnedClientsFromCompany?: number;
    shareOfPeerReferrals?: number;
    recurringAmount?: number;
    gmClient?: number;
    paybackPeriodOnCac?: number;
    m6MedianNetInvested?: number;
    cmPercentRevenue?: number;
    expansionArr?: number;
    expansionArrOverallArr?: number;
    headcount?: number;
    grossMarginDollar?: number;
    income?: number;
    arrCac?: number;
    churn?: number;
    ltvCac?: number;
}
export declare class UpsertKpiDetailsDto {
    KpiDetails: KpiDetailRecordDto[];
}
export type KpiDetailRecord = KpiDetailRecordDto;
