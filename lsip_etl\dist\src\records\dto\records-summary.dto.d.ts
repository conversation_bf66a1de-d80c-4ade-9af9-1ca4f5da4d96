import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
export declare class DatasetSummaryDto {
    key: string;
    label: string;
    count: number;
}
export declare class RecordsSummaryDataDto {
    totalRecords: number;
    datasets: DatasetSummaryDto[];
}
export declare class RecordsSummaryResponseDto extends BaseApiResponseDto {
    data: RecordsSummaryDataDto;
}
export declare class SyncedDatasetDto extends DatasetSummaryDto {
    synced: boolean;
}
export declare class SyncedRecordsDataDto {
    totalTrackedDatasets: number;
    totalDatasetsWithRecords: number;
    datasets: SyncedDatasetDto[];
}
export declare class SyncedRecordsResponseDto extends BaseApiResponseDto {
    data: SyncedRecordsDataDto;
}
export declare class SyncedRecordsQueryDto {
    includeZero?: string;
}
