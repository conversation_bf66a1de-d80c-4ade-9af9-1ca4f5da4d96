"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsKpiDetail = void 0;
const typeorm_1 = require("typeorm");
let JvsKpiDetail = class JvsKpiDetail {
    id;
    name = null;
    currencyIsoCode = null;
    companyId = null;
    aovRs = null;
    available = null;
    averageOrderValueProduct = null;
    averageOrderValue = null;
    businessUnitId = null;
    cacRs = null;
    cac = null;
    cm2 = null;
    cm3OfNmv = null;
    cmInLocalitiesWith500Users = null;
    cumulativeBankAccounts = null;
    bankAccountsCreatedPerMonth = null;
    cashInBank = null;
    categoryId = null;
    contributionMarginProduct = null;
    averageDepositBankAccount = null;
    contributionMargin = null;
    numberOfCreditCardIssued = null;
    customerAcquisitonCost = null;
    dau = null;
    dtrFromSource = null;
    date = null;
    cashBurnRsCr = null;
    cashBurn = null;
    ebitdaRsCr = null;
    ebitda = null;
    enable = null;
    firstPartyMix = null;
    firstParty = null;
    fulfillmentCostOfNmv = null;
    gmv = null;
    gmvFromPrivateLabels = null;
    gmvPerExistingBuyer = null;
    gmvPerFosCost = null;
    gmvPerNewBuyer = null;
    contributionMarginRsCr = null;
    grossMargin = null;
    grossMarginOfNmv = null;
    inventoryDays = null;
    latestFlag = null;
    m12GmvRetention = null;
    m12UserRetention = null;
    m1Repeat = null;
    m2GmvRetention = null;
    contributionMarginOfTakeRate = null;
    m2UserRetention = null;
    m6GmvRetention = null;
    m6UserRetention = null;
    discretionaryMarketing = null;
    grossMarginProduct = null;
    mau = null;
    marketingSpends = null;
    m2Retention = null;
    marketplaceFloat = null;
    merchantAcquisitionCost = null;
    nmv = null;
    nmvPerBuyerRs = null;
    noOfActiveTrucks = null;
    noOfDenseLanes = null;
    noOfDenseLocalities = null;
    noOfOrders = null;
    noOfPayingMerchants = null;
    noOfTrackedTrips = null;
    noOfTransactingBuyers = null;
    noOfTransactingUsers = null;
    noOfTransactions = null;
    macRs = null;
    numberOfBuyersNumber = null;
    numberOfLocalitiesWith500Users = null;
    numberOfNewUsers = null;
    numberOfTransactingUsersLakh = null;
    operatingCashBurnRsCr = null;
    orderFrequency = null;
    payableDays = null;
    payingMerchants = null;
    peopleGAOthersRsCr = null;
    mauLakh = null;
    marketing = null;
    receivableDays = null;
    recordTypeName = null;
    revenuetotal = null;
    salesCost = null;
    shippingCost = null;
    shippingCostOfNmv = null;
    takeRateGmv = null;
    takeRateRsCr = null;
    takeRate = null;
    takeRateByRevenueProduct = null;
    takeRateByRevenue = null;
    takeRateFromLocalitiesWith500User = null;
    takeRatePerUserRs = null;
    takeRateRevenueFromDenseLocalitie = null;
    takeRateRevenuePerExistingUser = null;
    takeRateRevenuePerNewUser = null;
    testKpi = null;
    totalPeopleCost = null;
    transactingBuyers = null;
    transactingStoresPerFos = null;
    transactingUserInLocalitiesWith500 = null;
    typeOfKpi = null;
    workingCapital = null;
    key = null;
    numberOfBuyers = null;
    revenueRsCr = null;
    cm1 = null;
    customerSupportBizOpsCost = null;
    underUtilizedFixedCostsInOperations = null;
    techProductCosts = null;
    sgA = null;
    buyerWithCredit = null;
    yim12MonthsAfterDpd = null;
    performanceMarketing = null;
    cmRsCr = null;
    takeRateOfLocalitiesWith500Users = null;
    performanceMarketingRsCr = null;
    cmInLocalities500UsersRsCr = null;
    payingUsers = null;
    m3PayingUserRetention = null;
    m3PayingRevenueRetention = null;
    numberOfAudioHeardPerDau = null;
    moArpu = null;
    totalArpu = null;
    cacDollar = null;
    m0Conversion = null;
    conversion = null;
    subscritpionIncome = null;
    marketingCost = null;
    employeeExpenseIndiaUs = null;
    ebitdaDollar = null;
    totalCashburn = null;
    nmvUsd = null;
    fmcgNmv = null;
    temanUlaNmv = null;
    othersNmv = null;
    takeRateNetMargin = null;
    takeRateFmcg = null;
    takeRateTermanUla = null;
    logisticsCost = null;
    fos = null;
    warehouse = null;
    cashInBankDollar = null;
    uniqueOrderingStores = null;
    m6StoreRetention = null;
    nmvPerStorePerMonth = null;
    aovDollar = null;
    ordersPerStorePerMonth = null;
    appRunningCostServerAnalyticsCos = null;
    marketingCostPercent = null;
    contentServingCost = null;
    otherBrandingExpenses = null;
    musicLicenseCost = null;
    employeeBenefitsExpenses = null;
    others = null;
    dauMau = null;
    retentionD30 = null;
    averageTimeSpent = null;
    uniqueCreators = null;
    totalInstalls = null;
    organic = null;
    costPerInstall = null;
    revenueDollar = null;
    skilling = null;
    recruitment = null;
    marketingOthers = null;
    salary = null;
    newInstalls = null;
    profileComplete = null;
    uninstallProfileCompletes = null;
    screenedLeads = null;
    fromExisting = null;
    fromReactivated = null;
    fromNew = null;
    screenedLeadsMau = null;
    m3UserRetention = null;
    repeatMauToLifeToDateUserBase = null;
    blendedCacForUser = null;
    newEmployerAcquired = null;
    activeEmployer = null;
    jobsActivated = null;
    m3EmployerRetention = null;
    repeatEmployerToLifeToDateEmployer = null;
    blendedCacEmployer = null;
    communityMauOfAppMau = null;
    communityToCommunityM1D30D60Rete = null;
    overallEnquiriesReceivedMtdYtd = null;
    conversionRateTrailing3Months = null;
    newOrdersForTheMonth = null;
    gtvArrOnSaas = null;
    gmvDollar = null;
    takeRateDollar = null;
    deliveredGmvForTheMonth = null;
    cumulativeOpenOrderBookClosing = null;
    cmPercent = null;
    numberOfLspsShippersAdded = null;
    activeShippersLsps = null;
    fixedExpenses = null;
    gmvFromRepeatCustomers = null;
    newCustomersAcquired = null;
    activeCustomers = null;
    q3CustomerRetention = null;
    activeSuppliers = null;
    q3SupplierRetention = null;
    arValueInRsCrs = null;
    apValueInRsCrs = null;
    inventoryValueInRsCrs = null;
    netWcArApInvAdv = null;
    wcConversionDays = null;
    freeCash = null;
    endingCarr = null;
    newCarrUpsell = null;
    monthlyChurnDowngrade = null;
    noOfCustomersCarr = null;
    carrPerCustomer = null;
    endingArr = null;
    newArrUpsell = null;
    monthlyChurnDowngradeArr = null;
    numberOfCustomersArr = null;
    arrPerCustomer = null;
    arrAsAOfCarr = null;
    platform = null;
    cpaas = null;
    voice = null;
    services = null;
    grossProfit = null;
    operatingIncome = null;
    dso = null;
    netRetention = null;
    averageCarrPerNewCustomer = null;
    carrFte = null;
    arrFte = null;
    annualisedNetChurn = null;
    ruleOf40 = null;
    netMagicNumber = null;
    salesCost000 = null;
    netRevenue = null;
    otherRevenueEGShippingRevenueEtc = null;
    cogs = null;
    allDiscountsRefundsEtc = null;
    wastage = null;
    parkingFuelLastMile = null;
    packagingCost = null;
    hubOperationsCost = null;
    cm2PercentageOfNetrevenue = null;
    marketingCostPercentageOfNetrevenue = null;
    cm3PercentOfNetRevenue = null;
    numberOfOrders = null;
    existingBuyers = null;
    ordersPerBuyer = null;
    existingBuyersNoBuyerPermonth = null;
    noOfUniqueBuyers = null;
    existingBuyers000 = null;
    gmvFromExistingBuyers = null;
    m3GmvRetention = null;
    gmvFromTop3Categories = null;
    grossBooking = null;
    newExpansion = null;
    downgradeChurn = null;
    ndr = null;
    salesMarketing = null;
    generalAdministrative = null;
    researchDevelopment = null;
    netBurn000 = null;
    burnMultiple = null;
    avgNewDealSize = null;
    eventCreated = null;
    eventHosted = null;
    eventsWithLessThan500Participants = null;
    eventsWith500To1000Participants = null;
    eventsWithGreater1000Participants = null;
    roiOnMarketingSpends = null;
    roiOnSalesSpends = null;
    salesQuotaAttainment = null;
    peopleRelatedSpend = null;
    cashBurnDollar000 = null;
    netDollarRetention = null;
    grossRetention = null;
    netSeatRetention = null;
    grossSeatRetention = null;
    logoRetention = null;
    totalSMSpend = null;
    sMEfficiency = null;
    newLogoArr = null;
    newLogoSeats = null;
    newLogo = null;
    newLogoAsp = null;
    newLogoAvgSeats = null;
    ebitdaBurn = null;
    totalBookings = null;
    global = null;
    smb = null;
    mm = null;
    enterprise = null;
    invoicedRevenue = null;
    gm = null;
    smbPercent = null;
    mmPercent = null;
    enterprisePercent = null;
    mlToMql = null;
    mqlToSql = null;
    sqlToClosed = null;
    grossRoi = null;
    outboundQuotaAttainment = null;
    enterpriseMmCac = null;
    smbInboundMarketingCac = null;
    saasLogoQ1Retention = null;
    saasRevenueQ1Retention = null;
    saasConversionSelfServeFunnel = null;
    marketplaceEnterpriseLogoQ1Reten = null;
    marketplaceEnterpriseRevenueQ1Re = null;
    marketplaceMmLogoQ1Retention = null;
    marketplaceMmRevenueQ1Retention = null;
    creatorEarnings = null;
    editorEarnings = null;
    cm1Percent = null;
    inboundQuotaAttainment = null;
    m3SpendPerCustomer = null;
    m3M0SpendPerCustomer = null;
    npas = null;
    moneyRemittedToIndiaOnZolve = null;
    discretionaryMarketingRsCr = null;
    takeRateOthers = null;
    subsRevenue = null;
    subsHostingCost = null;
    subsClientSupportCost = null;
    arrCarr = null;
    iarrContracted = null;
    employeesOnPlatformContracted = null;
    averageAcv = null;
    tradingVolume = null;
    tradingRevenue = null;
    cumulativeDownloads = null;
    cumulativeKycedUsers = null;
    numberOfActiveTraders = null;
    traderW24Retention = null;
    averageTradingFrequencyPerTrader = null;
    totalContractsValueArr = null;
    totalTerminalArr = null;
    terminalArrNetOfDiscounts = null;
    terminalArrAdded = null;
    terminalArrLost = null;
    nonRecurringRevenue = null;
    totalInstitutes = null;
    numberOfNewInstitutes = null;
    terminalArrInstitute = null;
    sMEfficiencyNetOfDiscounts = null;
    totalCreators = null;
    numberOfNewCreators = null;
    totalCustomers = null;
    terminalArrLostDollar = null;
    q2TripRetention = null;
    m6LspRetenAdjustedForPilotTrail = null;
    m6ShipperRetentionAdjustedTrials = null;
    numberOfSuppliersAdded = null;
    aumTotal = null;
    grossProfitPercent = null;
    aumCash = null;
    aumCpf = null;
    aumSrs = null;
    newInvestment = null;
    redemption = null;
    netNewMoneyInvested = null;
    existingClients = null;
    newClients = null;
    registeredAccounts = null;
    brokerAccounts = null;
    fundedClients = null;
    churnedClientsFromCompany = null;
    shareOfPeerReferrals = null;
    recurringAmount = null;
    gmClient = null;
    paybackPeriodOnCac = null;
    m6MedianNetInvested = null;
    cmPercentRevenue = null;
    expansionArr = null;
    expansionArrOverallArr = null;
    headcount = null;
    grossMarginDollar = null;
    income = null;
    arrCac = null;
    churn = null;
    ltvCac = null;
};
exports.JvsKpiDetail = JvsKpiDetail;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsKpiDetail.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'name', type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'currencyisocode', type: 'varchar', length: 3, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "currencyIsoCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'aov_rs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "aovRs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'available__c', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "available", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'average_order_value_product__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "averageOrderValueProduct", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'average_order_value__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "averageOrderValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'business_unit__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "businessUnitId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cac_rs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cacRs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cac__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cac", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm2__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cm2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm3_of_nmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cm3OfNmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm_in_localities_with_500_users__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cmInLocalitiesWith500Users", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cumulative_bank_accounts__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cumulativeBankAccounts", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'bank_accounts_created_per_month__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "bankAccountsCreatedPerMonth", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cash_in_bank__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cashInBank", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'category__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "categoryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contribution_margin_product__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "contributionMarginProduct", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'average_deposit_bank_account__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "averageDepositBankAccount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contribution_margin__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "contributionMargin", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_credit_card_issued__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfCreditCardIssued", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'customer_acquisiton_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "customerAcquisitonCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'dau__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "dau", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'dtr_from_source__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "dtrFromSource", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'date__c', type: 'date', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "date", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cash_burn_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cashBurnRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cash_burn__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cashBurn", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ebitda_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "ebitdaRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ebitda__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "ebitda", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'enable__c', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "enable", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'first_party_mix__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "firstPartyMix", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'first_party__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "firstParty", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fulfillment_cost_of_nmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "fulfillmentCostOfNmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gmv_from_private_labels__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmvFromPrivateLabels", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gmv_per_existing_buyer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmvPerExistingBuyer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gmv_per_fos_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmvPerFosCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gmv_per_new_buyer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmvPerNewBuyer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contribution_margin_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "contributionMarginRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_margin__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossMargin", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_margin_of_nmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossMarginOfNmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'inventory_days__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "inventoryDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'latest_flag__c', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "latestFlag", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm12_gmv_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m12GmvRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm12_user_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m12UserRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm1_repeat__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m1Repeat", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm2_gmv_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m2GmvRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contribution_margin_of_take_rate__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "contributionMarginOfTakeRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm2_user_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m2UserRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm6_gmv_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m6GmvRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm6_user_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m6UserRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'discretionary_marketing__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "discretionaryMarketing", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_margin_product__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossMarginProduct", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mau__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "mau", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketing_spends__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketingSpends", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm2_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m2Retention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketplace_float__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketplaceFloat", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'merchant_acquisition_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "merchantAcquisitionCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'nmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "nmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'nmv_per_buyer_rs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "nmvPerBuyerRs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_active_trucks__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfActiveTrucks", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_dense_lanes__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfDenseLanes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_dense_localities__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfDenseLocalities", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_orders__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfOrders", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_paying_merchants__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfPayingMerchants", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_tracked_trips__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfTrackedTrips", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_transacting_buyers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfTransactingBuyers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_transacting_users__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfTransactingUsers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_transactions__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfTransactions", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mac_rs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "macRs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_buyers_number__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfBuyersNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_localities_with_500_users__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfLocalitiesWith500Users", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_new_users__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfNewUsers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_transacting_users_lakh__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfTransactingUsersLakh", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'operating_cash_burn_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "operatingCashBurnRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'order_frequency__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "orderFrequency", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'payable_days__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "payableDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'paying_merchants__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "payingMerchants", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'people_g_a_others_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "peopleGAOthersRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mau_lakh__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "mauLakh", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketing__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketing", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'receivable_days__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "receivableDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'record_type_name__c', type: 'varchar', length: 1300, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "recordTypeName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'revenuetotal__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "revenuetotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sales_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "salesCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'shipping_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "shippingCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'shipping_cost_of_nmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "shippingCostOfNmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_gmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateGmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_by_revenue_product__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateByRevenueProduct", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_by_revenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateByRevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_from_localities_with_500_user__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateFromLocalitiesWith500User", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_per_user_rs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRatePerUserRs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_revenue_from_dense_localitie__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateRevenueFromDenseLocalitie", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_revenue_per_existing_user__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateRevenuePerExistingUser", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_revenue_per_new_user__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateRevenuePerNewUser", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'test_kpi__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "testKpi", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_people_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalPeopleCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'transacting_buyers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "transactingBuyers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'transacting_stores_per_fos__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "transactingStoresPerFos", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'transacting_user_in_localities_with_500__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "transactingUserInLocalitiesWith500", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'type_of_kpi__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "typeOfKpi", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'working_capital__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "workingCapital", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'key__c', type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "key", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_buyers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfBuyers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'revenue_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "revenueRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm1__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cm1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'customer_support_biz_ops_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "customerSupportBizOpsCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'under_utilized_fixed_costs_in_operations__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "underUtilizedFixedCostsInOperations", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'tech_product_costs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "techProductCosts", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sg_a__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "sgA", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'buyer_with_credit__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "buyerWithCredit", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'yim_12_months_after_dpd__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "yim12MonthsAfterDpd", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'performance_marketing__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "performanceMarketing", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cmRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_of_localities_with_500_users__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateOfLocalitiesWith500Users", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'performance_marketing_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "performanceMarketingRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm_in_localities_500_users_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cmInLocalities500UsersRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'paying_users__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "payingUsers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm3_paying_user_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m3PayingUserRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm3_paying_revenue_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m3PayingRevenueRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_audio_heard_per_dau__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfAudioHeardPerDau", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mo_arpu__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "moArpu", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_arpu__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalArpu", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cac_dollar__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cacDollar", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm0_conversion__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m0Conversion", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'conversion__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "conversion", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'subscritpion_income__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "subscritpionIncome", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketing_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketingCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'employee_expense_india_us__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "employeeExpenseIndiaUs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ebitda_dollar__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "ebitdaDollar", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_cashburn__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalCashburn", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'nmv_usd__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "nmvUsd", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fmcg_nmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "fmcgNmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'teman_ula_nmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "temanUlaNmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'others_nmv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "othersNmv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_net_margin__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateNetMargin", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_fmcg__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateFmcg", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_terman_ula__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateTermanUla", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'logistics_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "logisticsCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fos__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "fos", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'warehouse__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "warehouse", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cash_in_bank_dollar__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cashInBankDollar", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'unique_ordering_stores__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "uniqueOrderingStores", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm6_store_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m6StoreRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'nmv_per_store_per_month__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "nmvPerStorePerMonth", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'aov_dollar__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "aovDollar", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'orders_per_store_per_month__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "ordersPerStorePerMonth", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'app_running_cost_server_analytics_cos__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "appRunningCostServerAnalyticsCos", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketing_cost_percent__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketingCostPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'content_serving_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "contentServingCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'other_branding_expenses__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "otherBrandingExpenses", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'music_license_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "musicLicenseCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'employee_benefits_expenses__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "employeeBenefitsExpenses", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'others__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "others", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'dau_mau__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "dauMau", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retention_d30__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "retentionD30", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'average_time_spent__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "averageTimeSpent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'unique_creators__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "uniqueCreators", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_installs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalInstalls", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'organic__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "organic", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cost_per_install__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "costPerInstall", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'revenue_dollar__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "revenueDollar", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'skilling__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "skilling", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'recruitment__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "recruitment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketing_others__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketingOthers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'salary__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "salary", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_installs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newInstalls", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'profile_complete__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "profileComplete", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'uninstall_profile_completes__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "uninstallProfileCompletes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'screened_leads__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "screenedLeads", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'from_existing__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "fromExisting", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'from_reactivated__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "fromReactivated", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'from_new__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "fromNew", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'screened_leads_mau__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "screenedLeadsMau", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm3_user_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m3UserRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'repeat_mau_to_life_to_date_user_base__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "repeatMauToLifeToDateUserBase", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'blended_cac_for_user__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "blendedCacForUser", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_employer_acquired__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newEmployerAcquired", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'active_employer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "activeEmployer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'jobs_activated__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "jobsActivated", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm3_employer_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m3EmployerRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'repeat_employer_to_life_to_date_employer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "repeatEmployerToLifeToDateEmployer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'blended_cac_employer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "blendedCacEmployer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'community_mau_of_app_mau__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "communityMauOfAppMau", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'community_to_community_m1_d30_d60_rete__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "communityToCommunityM1D30D60Rete", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'overall_enquiries_received_mtd_ytd__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "overallEnquiriesReceivedMtdYtd", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'conversion_rate_trailing_3_months__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "conversionRateTrailing3Months", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_orders_for_the_month__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newOrdersForTheMonth", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gtv_arr_on_saas__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gtvArrOnSaas", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gmv_dollar__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmvDollar", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_dollar__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateDollar", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'delivered_gmv_for_the_month__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "deliveredGmvForTheMonth", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cumulative_open_order_book_closing__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cumulativeOpenOrderBookClosing", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm_percent__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cmPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_lsps_shippers_added__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfLspsShippersAdded", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'active_shippers_lsps__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "activeShippersLsps", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fixed_expenses__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "fixedExpenses", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gmv_from_repeat_customers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmvFromRepeatCustomers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_customers_acquired__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newCustomersAcquired", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'active_customers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "activeCustomers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'q3_customer_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "q3CustomerRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'active_suppliers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "activeSuppliers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'q3_supplier_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "q3SupplierRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ar_value_in_rs_crs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "arValueInRsCrs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ap_value_in_rs_crs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "apValueInRsCrs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'inventory_value_in_rs_crs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "inventoryValueInRsCrs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'net_wc_ar_ap_inv_adv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "netWcArApInvAdv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'wc_conversion_days__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "wcConversionDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'free_cash__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "freeCash", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ending_carr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "endingCarr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_carr_upsell__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newCarrUpsell", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'monthly_churn_downgrade__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "monthlyChurnDowngrade", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_customers_carr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfCustomersCarr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'carr_per_customer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "carrPerCustomer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ending_arr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "endingArr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_arr_upsell__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newArrUpsell", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'monthly_churn_downgrade_arr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "monthlyChurnDowngradeArr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_customers_arr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfCustomersArr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'arr_per_customer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "arrPerCustomer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'arr_as_a_of_carr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "arrAsAOfCarr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'platform__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "platform", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cpaas__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cpaas", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'voice__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "voice", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'services__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "services", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_profit__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossProfit", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'operating_income__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "operatingIncome", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'dso__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "dso", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'net_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "netRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'average_carr_per_new_customer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "averageCarrPerNewCustomer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'carr_fte__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "carrFte", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'arr_fte__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "arrFte", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'annualised_net_churn__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "annualisedNetChurn", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'rule_of_40__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "ruleOf40", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'net_magic_number__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "netMagicNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sales_cost_000__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "salesCost000", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'net_revenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "netRevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'other_revenue_e_g_shipping_revenue_etc__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "otherRevenueEGShippingRevenueEtc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cogs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cogs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'all_discounts_refunds_etc__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "allDiscountsRefundsEtc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'wastage__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "wastage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'parking_fuel_last_mile__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "parkingFuelLastMile", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'packaging_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "packagingCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'hub_operations_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "hubOperationsCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm2_percentage_of_netrevenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cm2PercentageOfNetrevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketing_cost_percentage_of_netrevenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketingCostPercentageOfNetrevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm3_percent_of_net_revenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cm3PercentOfNetRevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_orders__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfOrders", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'existing_buyers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "existingBuyers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'orders_per_buyer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "ordersPerBuyer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'existing_buyers_no_buyer_permonth__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "existingBuyersNoBuyerPermonth", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'no_of_unique_buyers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "noOfUniqueBuyers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'existing_buyers_000__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "existingBuyers000", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gmv_from_existing_buyers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmvFromExistingBuyers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm3_gmv_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m3GmvRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gmv_from_top_3_categories__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmvFromTop3Categories", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_booking__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossBooking", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_expansion__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newExpansion", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'downgrade_churn__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "downgradeChurn", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ndr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "ndr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sales_marketing__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "salesMarketing", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'general_administrative__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "generalAdministrative", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'research_development__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "researchDevelopment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'net_burn_000__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "netBurn000", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'burn_multiple__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "burnMultiple", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'avg_new_deal_size__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "avgNewDealSize", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'event_created__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "eventCreated", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'event_hosted__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "eventHosted", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'events_with_less_than_500_participants__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "eventsWithLessThan500Participants", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'events_with_500_to_1000_participants__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "eventsWith500To1000Participants", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'events_with_greater_1000_participants__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "eventsWithGreater1000Participants", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'roi_on_marketing_spends__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "roiOnMarketingSpends", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'roi_on_sales_spends__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "roiOnSalesSpends", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sales_quota_attainment__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "salesQuotaAttainment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'people_related_spend__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "peopleRelatedSpend", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cash_burn_dollar_000__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cashBurnDollar000", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'net_dollar_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "netDollarRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'net_seat_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "netSeatRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_seat_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossSeatRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'logo_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "logoRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_s_m_spend__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalSMSpend", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 's_m_efficiency__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "sMEfficiency", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_logo_arr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newLogoArr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_logo_seats__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newLogoSeats", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_logo__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newLogo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_logo_asp__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newLogoAsp", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_logo_avg_seats__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newLogoAvgSeats", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ebitda_burn__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "ebitdaBurn", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_bookings__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalBookings", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'global__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "global", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'smb__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "smb", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mm__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "mm", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'enterprise__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "enterprise", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'invoiced_revenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "invoicedRevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gm__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gm", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'smb_percent__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "smbPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mm_percent__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "mmPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'enterprise_percent__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "enterprisePercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ml_to_mql__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "mlToMql", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mql_to_sql__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "mqlToSql", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sql_to_closed__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "sqlToClosed", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_roi__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossRoi", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'outbound_quota_attainment__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "outboundQuotaAttainment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'enterprise_mm_cac__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "enterpriseMmCac", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'smb_inbound_marketing_cac__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "smbInboundMarketingCac", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'saas_logo_q1_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "saasLogoQ1Retention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'saas_revenue_q1_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "saasRevenueQ1Retention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'saas_conversion_self_serve_funnel__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "saasConversionSelfServeFunnel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketplace_enterprise_logo_q1_reten__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketplaceEnterpriseLogoQ1Reten", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketplace_enterprise_revenue_q1_re__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketplaceEnterpriseRevenueQ1Re", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketplace_mm_logo_q1_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketplaceMmLogoQ1Retention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'marketplace_mm_revenue_q1_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "marketplaceMmRevenueQ1Retention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'creator_earnings__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "creatorEarnings", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'editor_earnings__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "editorEarnings", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm1_percent__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cm1Percent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'inbound_quota_attainment__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "inboundQuotaAttainment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm3_spend_per_customer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m3SpendPerCustomer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm3_m0_spend_per_customer__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m3M0SpendPerCustomer", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'npas__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "npas", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'money_remitted_to_india_on_zolve__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "moneyRemittedToIndiaOnZolve", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'discretionary_marketing_rs_cr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "discretionaryMarketingRsCr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'take_rate_others__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "takeRateOthers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'subs_revenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "subsRevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'subs_hosting_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "subsHostingCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'subs_client_support_cost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "subsClientSupportCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'arr_carr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "arrCarr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'iarr_contracted__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "iarrContracted", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'employees_on_platform_contracted__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "employeesOnPlatformContracted", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'average_acv__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "averageAcv", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'trading_volume__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "tradingVolume", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'trading_revenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "tradingRevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cumulative_downloads__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cumulativeDownloads", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cumulative_kyced_users__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cumulativeKycedUsers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_active_traders__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfActiveTraders", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'trader_w24_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "traderW24Retention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'average_trading_frequency_per_trader__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "averageTradingFrequencyPerTrader", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_contracts_value_arr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalContractsValueArr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_terminal_arr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalTerminalArr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'terminal_arr_net_of_discounts__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "terminalArrNetOfDiscounts", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'terminal_arr_added__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "terminalArrAdded", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'terminal_arr_lost__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "terminalArrLost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'non_recurring_revenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "nonRecurringRevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_institutes__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalInstitutes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_new_institutes__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfNewInstitutes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'terminal_arr_institute__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "terminalArrInstitute", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 's_m_efficiency_net_of_discounts__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "sMEfficiencyNetOfDiscounts", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_creators__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalCreators", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_new_creators__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfNewCreators", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_customers__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "totalCustomers", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'terminal_arr_lost_dollar__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "terminalArrLostDollar", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'q2_trip_retention__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "q2TripRetention", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm6_lsp_reten_adjusted_for_pilot_trail__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m6LspRetenAdjustedForPilotTrail", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm6_shipper_retention_adjusted_trials__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m6ShipperRetentionAdjustedTrials", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_suppliers_added__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "numberOfSuppliersAdded", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'aum_total__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "aumTotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_profit_percent__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossProfitPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'aum_cash__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "aumCash", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'aum_cpf__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "aumCpf", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'aum_srs__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "aumSrs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_investment__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newInvestment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'redemption__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "redemption", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'net_new_money_invested__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "netNewMoneyInvested", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'existing_clients__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "existingClients", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'new_clients__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "newClients", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'registered_accounts__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "registeredAccounts", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'broker_accounts__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "brokerAccounts", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'funded_clients__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "fundedClients", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'churned_clients_from_company__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "churnedClientsFromCompany", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'share_of_peer_referrals__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "shareOfPeerReferrals", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'recurring_amount__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "recurringAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gm_client__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "gmClient", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'payback_period_on_cac__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "paybackPeriodOnCac", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'm6_median_net_invested__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "m6MedianNetInvested", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cm_percent_revenue__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "cmPercentRevenue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'expansion_arr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "expansionArr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'expansion_arr_overall_arr__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "expansionArrOverallArr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'headcount__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "headcount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gross_margin_dollar__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "grossMarginDollar", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'income__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "income", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'arr_cac__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "arrCac", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'churn__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "churn", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ltv_cac__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiDetail.prototype, "ltvCac", void 0);
exports.JvsKpiDetail = JvsKpiDetail = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_kpi_details' })
], JvsKpiDetail);
//# sourceMappingURL=jvs-kpi-detail.entity.js.map