"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalesforceModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const salesforce_service_1 = require("./salesforce.service");
let SalesforceModule = class SalesforceModule {
};
exports.SalesforceModule = SalesforceModule;
exports.SalesforceModule = SalesforceModule = __decorate([
    (0, common_1.Module)({
        imports: [
            axios_1.HttpModule.registerAsync({
                inject: [config_1.ConfigService],
                useFactory: (configService) => ({
                    baseURL: configService.get('salesforce.authUrl'),
                    timeout: 15000,
                    maxRedirects: 3,
                }),
            }),
        ],
        providers: [salesforce_service_1.SalesforceService],
        exports: [salesforce_service_1.SalesforceService],
    })
], SalesforceModule);
//# sourceMappingURL=salesforce.module.js.map