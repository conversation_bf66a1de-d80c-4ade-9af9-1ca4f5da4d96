const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// Configuration
const SERVER_URL = 'http://localhost:3000'; // Change this if your server runs on a different port
const ENDPOINT = '/company-funds';
const JSON_FILE_PATH = path.join(__dirname, 'src', 'ExcelJsonData', 'Cap table.json');

async function sendCapTableData() {
  try {
    // Read the JSON file
    console.log('Reading JSON file:', JSON_FILE_PATH);
    
    if (!fs.existsSync(JSON_FILE_PATH)) {
      throw new Error(`JSON file not found at: ${JSON_FILE_PATH}`);
    }
    
    const jsonData = fs.readFileSync(JSON_FILE_PATH, 'utf8');
    const payload = JSON.parse(jsonData);
    
    console.log('Loaded data:', JSON.stringify(payload, null, 2));
    console.log(`Found ${payload.CompanyFunds?.length || 0} company fund records`);
    
    // Prepare the HTTP request
    const url = new URL(SERVER_URL + ENDPOINT);
    const postData = JSON.stringify(payload);
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    console.log(`\nSending POST request to: ${SERVER_URL}${ENDPOINT}`);
    console.log('Request options:', options);
    
    // Send the request
    const response = await makeRequest(options, postData);
    
    console.log('\n✅ Success! Response received:');
    console.log('Status:', response.statusCode);
    console.log('Headers:', response.headers);
    console.log('Body:', response.body);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Make sure your NestJS server is running on', SERVER_URL);
      console.error('   You can start it with: npm run start:dev');
    }
    process.exit(1);
  }
}

function makeRequest(options, postData) {
  return new Promise((resolve, reject) => {
    const protocol = options.port === 443 ? https : http;
    
    const req = protocol.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedBody
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.write(postData);
    req.end();
  });
}

// Run the script
if (require.main === module) {
  sendCapTableData();
}

module.exports = { sendCapTableData };
