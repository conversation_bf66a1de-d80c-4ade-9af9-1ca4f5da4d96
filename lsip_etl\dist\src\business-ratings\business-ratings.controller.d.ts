import { BusinessRatingsService } from './business-ratings.service';
import { UpsertBusinessRatingsDto } from './dto/upsert-business-ratings.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class BusinessRatingsController {
    private readonly businessRatingsService;
    private static readonly validationPipe;
    constructor(businessRatingsService: BusinessRatingsService);
    upsertBusinessRatings(payload: UpsertBusinessRatingsDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            accountAttempted: string | null;
        }[];
    }>>;
}
