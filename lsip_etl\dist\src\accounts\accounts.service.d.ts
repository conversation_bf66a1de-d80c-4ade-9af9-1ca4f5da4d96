import { Repository } from 'typeorm';
import { JvsAccount } from './jvs-account.entity';
import { AccountRecord } from './dto/upsert-accounts.dto';
import { JvsIndustry } from '../industries/jvs-industry.entity';
interface UnprocessedAccountRecord extends AccountRecord {
    message: string;
    parentAccountAttempted: string | null;
    industryMasterAttempted: string | null;
    subIndustryAttempted: string | null;
}
interface UpsertAccountsResult {
    processed: number;
    unprocessedRecords: UnprocessedAccountRecord[];
}
export declare class AccountsService {
    private readonly accountRepository;
    private readonly industryRepository;
    constructor(accountRepository: Repository<JvsAccount>, industryRepository: Repository<JvsIndustry>);
    upsertAccounts(records: AccountRecord[]): Promise<UpsertAccountsResult>;
}
export {};
