"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessUnitsApiResponseDto = exports.BusinessUnitsProcessedDataDto = exports.UnprocessedBusinessUnitRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_business_units_dto_1 = require("./upsert-business-units.dto");
class UnprocessedBusinessUnitRecordDto extends upsert_business_units_dto_1.BusinessUnitRecordDto {
    message;
    companyAttempted;
    parentBusinessAttempted;
}
exports.UnprocessedBusinessUnitRecordDto = UnprocessedBusinessUnitRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedBusinessUnitRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedBusinessUnitRecordDto.prototype, "companyAttempted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Parent business unit identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedBusinessUnitRecordDto.prototype, "parentBusinessAttempted", void 0);
class BusinessUnitsProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.BusinessUnitsProcessedDataDto = BusinessUnitsProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of business units processed.' }),
    __metadata("design:type", Number)
], BusinessUnitsProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings.',
        type: () => [UnprocessedBusinessUnitRecordDto],
    }),
    __metadata("design:type", Array)
], BusinessUnitsProcessedDataDto.prototype, "unprocessedRecords", void 0);
class BusinessUnitsApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.BusinessUnitsApiResponseDto = BusinessUnitsApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: BusinessUnitsProcessedDataDto }),
    __metadata("design:type", BusinessUnitsProcessedDataDto)
], BusinessUnitsApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=business-units-response.dto.js.map