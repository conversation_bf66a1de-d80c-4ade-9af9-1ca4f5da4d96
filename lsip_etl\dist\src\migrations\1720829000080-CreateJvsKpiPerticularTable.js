"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJvsKpiPerticularTable1720829000080 = void 0;
class CreateJvsKpiPerticularTable1720829000080 {
    name = 'CreateJvsKpiPerticularTable1720829000080';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_kpi_perticular (
              id character varying(18) NOT NULL,
              name character varying(80),
              currencyisocode character varying(3),
              business_unit__c character varying(18),
              company__c character varying(18),
              creation_type__c character varying(255),
              formulaid__c character varying(32768),
              formula__c character varying(131072),
              kpi_master__c character varying(18),
              kpi_particular_level_number__c double precision,
              kpi_particular_level__c character varying(255),
              name_set__c boolean,
              name__c character varying(255),
              plan_required__c boolean,
              sub_business_unit__c character varying(18),
              unique_identifier__c character varying(225),
              benchmark__c character varying(18),
              industry__c character varying(18),
              kpi_particular_type__c character varying(255),
              sub_industry__c character varying(18),
              entity_id__c character varying(1300),
              CONSTRAINT pk_jvs_kpi_perticular_id PRIMARY KEY (id)
            );
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_perticular_company__c ON jvs_kpi_perticular (company__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_perticular_business_unit__c ON jvs_kpi_perticular (business_unit__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_perticular_sub_business_unit__c ON jvs_kpi_perticular (sub_business_unit__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_perticular_kpi_master__c ON jvs_kpi_perticular (kpi_master__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_perticular_industry__c ON jvs_kpi_perticular (industry__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_perticular_sub_industry__c ON jvs_kpi_perticular (sub_industry__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_perticular_unique_identifier__c ON jvs_kpi_perticular (unique_identifier__c);
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular ADD CONSTRAINT fk_jvs_kpi_perticular_company__c FOREIGN KEY (company__c) REFERENCES jvs_accounts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular ADD CONSTRAINT fk_jvs_kpi_perticular_business_unit__c FOREIGN KEY (business_unit__c) REFERENCES jvs_business_units(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular ADD CONSTRAINT fk_jvs_kpi_perticular_sub_business_unit__c FOREIGN KEY (sub_business_unit__c) REFERENCES jvs_business_units(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular ADD CONSTRAINT fk_jvs_kpi_perticular_kpi_master__c FOREIGN KEY (kpi_master__c) REFERENCES jvs_kpi_master(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular ADD CONSTRAINT fk_jvs_kpi_perticular_industry__c FOREIGN KEY (industry__c) REFERENCES jvs_industries(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular ADD CONSTRAINT fk_jvs_kpi_perticular_sub_industry__c FOREIGN KEY (sub_industry__c) REFERENCES jvs_industries(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular DROP CONSTRAINT IF EXISTS fk_jvs_kpi_perticular_sub_industry__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular DROP CONSTRAINT IF EXISTS fk_jvs_kpi_perticular_industry__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular DROP CONSTRAINT IF EXISTS fk_jvs_kpi_perticular_kpi_master__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular DROP CONSTRAINT IF EXISTS fk_jvs_kpi_perticular_sub_business_unit__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular DROP CONSTRAINT IF EXISTS fk_jvs_kpi_perticular_business_unit__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_perticular DROP CONSTRAINT IF EXISTS fk_jvs_kpi_perticular_company__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_perticular_unique_identifier__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_perticular_sub_industry__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_perticular_industry__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_perticular_kpi_master__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_perticular_sub_business_unit__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_perticular_business_unit__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_perticular_company__c;
    `);
        await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_kpi_perticular;
    `);
    }
}
exports.CreateJvsKpiPerticularTable1720829000080 = CreateJvsKpiPerticularTable1720829000080;
//# sourceMappingURL=1720829000080-CreateJvsKpiPerticularTable.js.map