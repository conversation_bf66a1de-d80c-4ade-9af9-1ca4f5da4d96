"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecordsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
const jvs_business_rating_entity_1 = require("../business-ratings/jvs-business-rating.entity");
const jvs_business_unit_entity_1 = require("../business-units/jvs-business-unit.entity");
const jvs_company_fund_entity_1 = require("../company-funds/jvs-company-fund.entity");
const jvs_contact_entity_1 = require("../contacts/jvs-contact.entity");
const jvs_founder_rating_entity_1 = require("../founder-ratings/jvs-founder-rating.entity");
const jvs_industry_entity_1 = require("../industries/jvs-industry.entity");
const jvs_kpi_detail_entity_1 = require("../kpi-details/jvs-kpi-detail.entity");
const jvs_kpi_master_entity_1 = require("../kpi-master/jvs-kpi-master.entity");
const jvs_kpi_perticular_entity_1 = require("../kpi-perticular/jvs-kpi-perticular.entity");
const jvs_kpi_plan_actual_entity_1 = require("../kpi-plan-actual/jvs-kpi-plan-actual.entity");
const jvs_kpi_relevancy_entity_1 = require("../kpi-relevancy/jvs-kpi-relevancy.entity");
const jvs_vector_master_entity_1 = require("../vector-master/jvs-vector-master.entity");
const jvs_vector_detail_entity_1 = require("../vector-detail/jvs-vector-detail.entity");
const jvs_vector_plan_actual_entity_1 = require("../vector-plan-actual/jvs-vector-plan-actual.entity");
let RecordsService = class RecordsService {
    accountsRepository;
    businessRatingsRepository;
    businessUnitsRepository;
    companyFundsRepository;
    contactsRepository;
    founderRatingsRepository;
    industriesRepository;
    kpiDetailsRepository;
    kpiMasterRepository;
    kpiPerticularRepository;
    kpiPlanActualRepository;
    kpiRelevancyRepository;
    vectorMasterRepository;
    vectorDetailRepository;
    vectorPlanActualRepository;
    constructor(accountsRepository, businessRatingsRepository, businessUnitsRepository, companyFundsRepository, contactsRepository, founderRatingsRepository, industriesRepository, kpiDetailsRepository, kpiMasterRepository, kpiPerticularRepository, kpiPlanActualRepository, kpiRelevancyRepository, vectorMasterRepository, vectorDetailRepository, vectorPlanActualRepository) {
        this.accountsRepository = accountsRepository;
        this.businessRatingsRepository = businessRatingsRepository;
        this.businessUnitsRepository = businessUnitsRepository;
        this.companyFundsRepository = companyFundsRepository;
        this.contactsRepository = contactsRepository;
        this.founderRatingsRepository = founderRatingsRepository;
        this.industriesRepository = industriesRepository;
        this.kpiDetailsRepository = kpiDetailsRepository;
        this.kpiMasterRepository = kpiMasterRepository;
        this.kpiPerticularRepository = kpiPerticularRepository;
        this.kpiPlanActualRepository = kpiPlanActualRepository;
        this.kpiRelevancyRepository = kpiRelevancyRepository;
        this.vectorMasterRepository = vectorMasterRepository;
        this.vectorDetailRepository = vectorDetailRepository;
        this.vectorPlanActualRepository = vectorPlanActualRepository;
    }
    getTrackedRepositories() {
        return [
            { key: 'accounts', label: 'Accounts', repository: this.accountsRepository },
            { key: 'businessRatings', label: 'Business Ratings', repository: this.businessRatingsRepository },
            { key: 'businessUnits', label: 'Business Units', repository: this.businessUnitsRepository },
            { key: 'companyFunds', label: 'Company Funds', repository: this.companyFundsRepository },
            { key: 'contacts', label: 'Contacts', repository: this.contactsRepository },
            { key: 'founderRatings', label: 'Founder Ratings', repository: this.founderRatingsRepository },
            { key: 'industries', label: 'Industries', repository: this.industriesRepository },
            { key: 'kpiDetails', label: 'KPI Details', repository: this.kpiDetailsRepository },
            { key: 'kpiMaster', label: 'KPI Master', repository: this.kpiMasterRepository },
            { key: 'kpiPerticular', label: 'KPI Particular', repository: this.kpiPerticularRepository },
            { key: 'kpiPlanActual', label: 'KPI Plan vs Actual', repository: this.kpiPlanActualRepository },
            { key: 'kpiRelevancy', label: 'KPI Relevancy', repository: this.kpiRelevancyRepository },
            { key: 'vectorMaster', label: 'Vector Master', repository: this.vectorMasterRepository },
            { key: 'vectorDetail', label: 'Vector Detail', repository: this.vectorDetailRepository },
            { key: 'vectorPlanActual', label: 'Vector Plan vs Actual', repository: this.vectorPlanActualRepository },
        ];
    }
    async collectCounts() {
        const descriptors = this.getTrackedRepositories();
        const datasetSummaries = await Promise.all(descriptors.map(async ({ key, label, repository }) => {
            const count = await repository.count();
            return { key, label, count };
        }));
        return datasetSummaries;
    }
    async getRecordsSummary() {
        const datasets = await this.collectCounts();
        const totalRecords = datasets.reduce((acc, dataset) => acc + dataset.count, 0);
        return {
            totalRecords,
            datasets,
        };
    }
    async getSyncedRecords(includeZero) {
        const datasets = await this.collectCounts();
        const datasetsWithRecords = datasets.filter((dataset) => dataset.count > 0);
        const visibleDatasets = includeZero ? datasets : datasetsWithRecords;
        return {
            totalTrackedDatasets: datasets.length,
            totalDatasetsWithRecords: datasetsWithRecords.length,
            datasets: visibleDatasets.map((dataset) => ({
                ...dataset,
                synced: dataset.count > 0,
            })),
        };
    }
};
exports.RecordsService = RecordsService;
exports.RecordsService = RecordsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_business_rating_entity_1.JvsBusinessRating)),
    __param(2, (0, typeorm_1.InjectRepository)(jvs_business_unit_entity_1.JvsBusinessUnit)),
    __param(3, (0, typeorm_1.InjectRepository)(jvs_company_fund_entity_1.JvsCompanyFund)),
    __param(4, (0, typeorm_1.InjectRepository)(jvs_contact_entity_1.JvsContact)),
    __param(5, (0, typeorm_1.InjectRepository)(jvs_founder_rating_entity_1.JvsFounderRating)),
    __param(6, (0, typeorm_1.InjectRepository)(jvs_industry_entity_1.JvsIndustry)),
    __param(7, (0, typeorm_1.InjectRepository)(jvs_kpi_detail_entity_1.JvsKpiDetail)),
    __param(8, (0, typeorm_1.InjectRepository)(jvs_kpi_master_entity_1.JvsKpiMaster)),
    __param(9, (0, typeorm_1.InjectRepository)(jvs_kpi_perticular_entity_1.JvsKpiPerticular)),
    __param(10, (0, typeorm_1.InjectRepository)(jvs_kpi_plan_actual_entity_1.JvsKpiPlanActual)),
    __param(11, (0, typeorm_1.InjectRepository)(jvs_kpi_relevancy_entity_1.JvsKpiRelevancy)),
    __param(12, (0, typeorm_1.InjectRepository)(jvs_vector_master_entity_1.JvsVectorMaster)),
    __param(13, (0, typeorm_1.InjectRepository)(jvs_vector_detail_entity_1.JvsVectorDetail)),
    __param(14, (0, typeorm_1.InjectRepository)(jvs_vector_plan_actual_entity_1.JvsVectorPlanActual)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], RecordsService);
//# sourceMappingURL=records.service.js.map