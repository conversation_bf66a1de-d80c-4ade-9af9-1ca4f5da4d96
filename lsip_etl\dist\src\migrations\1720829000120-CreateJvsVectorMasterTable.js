"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJvsVectorMasterTable1720829000120 = void 0;
class CreateJvsVectorMasterTable1720829000120 {
    name = 'CreateJvsVectorMasterTable1720829000120';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_vector_master (
              id character varying(18) NOT NULL,
              name character varying(80),
              currencyisocode character varying(3),
              CONSTRAINT pk_jvs_vector_master_id PRIMARY KEY (id)
            );
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_vector_master;
    `);
    }
}
exports.CreateJvsVectorMasterTable1720829000120 = CreateJvsVectorMasterTable1720829000120;
//# sourceMappingURL=1720829000120-CreateJvsVectorMasterTable.js.map