"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const accounts_service_1 = require("./accounts.service");
const upsert_accounts_dto_1 = require("./dto/upsert-accounts.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const accounts_response_dto_1 = require("./dto/accounts-response.dto");
let AccountsController = class AccountsController {
    accountsService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(accountsService) {
        this.accountsService = accountsService;
    }
    async upsertAccounts(payload) {
        const result = await this.accountsService.upsertAccounts(payload.Accounts);
        return (0, api_response_dto_1.createSuccessResponse)('Accounts processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.AccountsController = AccountsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(AccountsController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert accounts data.' }),
    (0, swagger_1.ApiBody)({ type: upsert_accounts_dto_1.UpsertAccountsDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Accounts processed successfully.',
        type: accounts_response_dto_1.AccountsApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_accounts_dto_1.UpsertAccountsDto]),
    __metadata("design:returntype", Promise)
], AccountsController.prototype, "upsertAccounts", null);
exports.AccountsController = AccountsController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('accounts'),
    __metadata("design:paramtypes", [accounts_service_1.AccountsService])
], AccountsController);
//# sourceMappingURL=accounts.controller.js.map