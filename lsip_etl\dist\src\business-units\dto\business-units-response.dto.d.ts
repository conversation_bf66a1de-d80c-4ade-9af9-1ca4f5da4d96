import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { BusinessUnitRecordDto } from './upsert-business-units.dto';
export declare class UnprocessedBusinessUnitRecordDto extends BusinessUnitRecordDto {
    message: string;
    companyAttempted: string | null;
    parentBusinessAttempted: string | null;
}
export declare class BusinessUnitsProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedBusinessUnitRecordDto[];
}
export declare class BusinessUnitsApiResponseDto extends BaseApiResponseDto {
    data: BusinessUnitsProcessedDataDto;
}
