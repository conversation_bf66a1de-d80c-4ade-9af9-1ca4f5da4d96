"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createErrorResponse = exports.createSuccessResponse = exports.StringResponseDto = exports.BaseApiResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class BaseApiResponseDto {
    status;
    message;
    error;
    data;
}
exports.BaseApiResponseDto = BaseApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['success', 'error'] }),
    __metadata("design:type", String)
], BaseApiResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], BaseApiResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", Object)
], BaseApiResponseDto.prototype, "error", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", Object)
], BaseApiResponseDto.prototype, "data", void 0);
class StringResponseDto extends BaseApiResponseDto {
}
exports.StringResponseDto = StringResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], StringResponseDto.prototype, "data", void 0);
const createSuccessResponse = (message, data) => ({
    status: 'success',
    message,
    data,
    error: null,
});
exports.createSuccessResponse = createSuccessResponse;
const createErrorResponse = (message, error, data = null) => ({
    status: 'error',
    message,
    data,
    error,
});
exports.createErrorResponse = createErrorResponse;
//# sourceMappingURL=api-response.dto.js.map