import { Repository } from 'typeorm';
import { JvsCompanyFund } from './jvs-company-fund.entity';
import { CompanyFundRecord } from './dto/upsert-company-funds.dto';
import { JvsAccount } from '../accounts/jvs-account.entity';
interface UnprocessedCompanyFundRecord extends CompanyFundRecord {
    message: string;
    accountAttempted: string | null;
}
interface UpsertCompanyFundsResult {
    processed: number;
    unprocessedRecords: UnprocessedCompanyFundRecord[];
}
export declare class CompanyFundsService {
    private readonly companyFundRepository;
    private readonly accountRepository;
    constructor(companyFundRepository: Repository<JvsCompanyFund>, accountRepository: Repository<JvsAccount>);
    upsertCompanyFunds(records: CompanyFundRecord[]): Promise<UpsertCompanyFundsResult>;
}
export {};
