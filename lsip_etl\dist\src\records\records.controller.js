"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecordsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const records_summary_dto_1 = require("./dto/records-summary.dto");
const records_service_1 = require("./records.service");
let RecordsController = class RecordsController {
    recordsService;
    constructor(recordsService) {
        this.recordsService = recordsService;
    }
    async getRecordsSummary() {
        const data = await this.recordsService.getRecordsSummary();
        return (0, api_response_dto_1.createSuccessResponse)('Record counts aggregated successfully.', data);
    }
    async getSyncedRecords(query) {
        const includeZero = query.includeZero === 'true';
        const data = await this.recordsService.getSyncedRecords(includeZero);
        return (0, api_response_dto_1.createSuccessResponse)('Synced datasets fetched successfully.', data);
    }
};
exports.RecordsController = RecordsController;
__decorate([
    (0, common_1.Get)('summary'),
    (0, swagger_1.ApiOperation)({ summary: 'Get per-entity record counts and totals.' }),
    (0, swagger_1.ApiOkResponse)({ type: records_summary_dto_1.RecordsSummaryResponseDto }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RecordsController.prototype, "getRecordsSummary", null);
__decorate([
    (0, common_1.Get)('synced'),
    (0, swagger_1.ApiOperation)({
        summary: 'List datasets that have already been synced into the system.',
    }),
    (0, swagger_1.ApiOkResponse)({ type: records_summary_dto_1.SyncedRecordsResponseDto }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [records_summary_dto_1.SyncedRecordsQueryDto]),
    __metadata("design:returntype", Promise)
], RecordsController.prototype, "getSyncedRecords", null);
exports.RecordsController = RecordsController = __decorate([
    (0, swagger_1.ApiTags)('Records'),
    (0, common_1.Controller)('records'),
    __metadata("design:paramtypes", [records_service_1.RecordsService])
], RecordsController);
//# sourceMappingURL=records.controller.js.map