import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { KpiDetailRecordDto } from './upsert-kpi-details.dto';
export declare class UnprocessedKpiDetailRecordDto extends KpiDetailRecordDto {
    message: string;
    companyAttempted: string | null;
    businessUnitAttempted: string | null;
}
export declare class KpiDetailsProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedKpiDetailRecordDto[];
}
export declare class KpiDetailsApiResponseDto extends BaseApiResponseDto {
    data: KpiDetailsProcessedDataDto;
}
