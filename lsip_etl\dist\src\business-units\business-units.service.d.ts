import { Repository } from 'typeorm';
import { JvsBusinessUnit } from './jvs-business-unit.entity';
import { BusinessUnitRecord } from './dto/upsert-business-units.dto';
import { JvsAccount } from '../accounts/jvs-account.entity';
interface UnprocessedBusinessUnitRecord extends BusinessUnitRecord {
    message: string;
    companyAttempted: string | null;
    parentBusinessAttempted: string | null;
}
interface UpsertBusinessUnitsResult {
    processed: number;
    unprocessedRecords: UnprocessedBusinessUnitRecord[];
}
export declare class BusinessUnitsService {
    private readonly businessUnitRepository;
    private readonly accountRepository;
    constructor(businessUnitRepository: Repository<JvsBusinessUnit>, accountRepository: Repository<JvsAccount>);
    upsertBusinessUnits(records: BusinessUnitRecord[]): Promise<UpsertBusinessUnitsResult>;
}
export {};
