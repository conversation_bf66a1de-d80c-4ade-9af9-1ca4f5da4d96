"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertBusinessRatingsDto = exports.BusinessRatingRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class BusinessRatingRecordDto {
    id;
    name;
    createddate;
    account__c;
    product_vision_strategy__c;
    product_vision_strategy_des__c;
    gtm_muscle_ent_sales_partnership_des__c;
    gtm_muscle_ent_sales_partnership__c;
    data_technology_orientation__c;
    data_technology_orientation_des__c;
    deep_tech_engg_product_leadership__c;
    deep_tech_engg_product_leadership_des__c;
    gtm_muscle_commercialization__c;
    gtm_muscle_commercialization_des__c;
    regulatory__c;
    regulatory_des__c;
    product_vision_strategy_ai_ml__c;
    product_vision_strategy_ai_ml_des__c;
    gtm_muscle_for_devtools_prosumer__c;
    gtm_muscle_for_devtools_prosumer_des__c;
    narrative_building__c;
    narrative_building_desc__c;
    product_vision_strategy_ai_llm__c;
    product_vision_strategy_ai_llm_desc__c;
    gtm_muscle__c;
    gtm_muscle_desc__c;
    positioning_narrative_building__c;
    positioning_narrative_building_desc__c;
    product_strategy_strategic_thinking__c;
    product_strategy_strategic_thinking_des__c;
    marketing_narrative_building__c;
    marketing_narrative_building_desc__c;
    ai_capability__c;
    ai_capability_desc__c;
    category_creation__c;
    category_creation_desc__c;
}
exports.BusinessRatingRecordDto = BusinessRatingRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the business rating.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Rating name.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Timestamp when the rating was created.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "createddate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Associated account identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "account__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for product vision and strategy.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "product_vision_strategy__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for product vision and strategy.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "product_vision_strategy_des__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for GTM muscle (enterprise sales & partnerships).', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "gtm_muscle_ent_sales_partnership_des__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for GTM muscle (enterprise sales & partnerships).', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "gtm_muscle_ent_sales_partnership__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for data/technology orientation.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "data_technology_orientation__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for data/technology orientation.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "data_technology_orientation_des__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for deep tech/engineering/product leadership.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "deep_tech_engg_product_leadership__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for deep tech/engineering/product leadership.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "deep_tech_engg_product_leadership_des__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for GTM muscle (commercialization).', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "gtm_muscle_commercialization__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for GTM muscle (commercialization).', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "gtm_muscle_commercialization_des__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for regulatory readiness.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "regulatory__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for regulatory readiness.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "regulatory_des__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for product vision & strategy (AI/ML).', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "product_vision_strategy_ai_ml__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for product vision & strategy (AI/ML).', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "product_vision_strategy_ai_ml_des__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for GTM muscle (devtools/prosumer).', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "gtm_muscle_for_devtools_prosumer__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for GTM muscle (devtools/prosumer).', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "gtm_muscle_for_devtools_prosumer_des__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for narrative building.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "narrative_building__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for narrative building.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "narrative_building_desc__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for product vision & strategy (AI/LLM).', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "product_vision_strategy_ai_llm__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for product vision & strategy (AI/LLM).', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "product_vision_strategy_ai_llm_desc__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for GTM muscle overall.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "gtm_muscle__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for GTM muscle overall.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "gtm_muscle_desc__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for positioning and narrative building.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "positioning_narrative_building__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for positioning and narrative building.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "positioning_narrative_building_desc__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for product strategy and strategic thinking.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "product_strategy_strategic_thinking__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for product strategy and strategic thinking.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "product_strategy_strategic_thinking_des__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for marketing narrative building.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "marketing_narrative_building__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for marketing narrative building.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "marketing_narrative_building_desc__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for AI capability.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "ai_capability__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for AI capability.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "ai_capability_desc__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for category creation.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "category_creation__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Narrative for category creation.', maxLength: 4000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(4000),
    __metadata("design:type", String)
], BusinessRatingRecordDto.prototype, "category_creation_desc__c", void 0);
class UpsertBusinessRatingsDto {
    BusinessRatings;
}
exports.UpsertBusinessRatingsDto = UpsertBusinessRatingsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [BusinessRatingRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => BusinessRatingRecordDto),
    __metadata("design:type", Array)
], UpsertBusinessRatingsDto.prototype, "BusinessRatings", void 0);
//# sourceMappingURL=upsert-business-ratings.dto.js.map