import { Repository } from 'typeorm';
import { JvsFounderRating } from './jvs-founder-rating.entity';
import { FounderRatingRecord } from './dto/upsert-founder-ratings.dto';
import { JvsAccount } from '../accounts/jvs-account.entity';
interface UnprocessedFounderRatingRecord extends FounderRatingRecord {
    message: string;
    accountAttempted: string | null;
}
interface UpsertFounderRatingsResult {
    processed: number;
    unprocessedRecords: UnprocessedFounderRatingRecord[];
}
export declare class FounderRatingsService {
    private readonly founderRatingRepository;
    private readonly accountRepository;
    constructor(founderRatingRepository: Repository<JvsFounderRating>, accountRepository: Repository<JvsAccount>);
    upsertFounderRatings(records: FounderRatingRecord[]): Promise<UpsertFounderRatingsResult>;
}
export {};
