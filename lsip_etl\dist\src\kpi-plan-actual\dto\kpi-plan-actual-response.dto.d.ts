import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { KpiPlanActualRecordDto } from './upsert-kpi-plan-actual.dto';
export declare class UnprocessedKpiPlanActualRecordDto extends KpiPlanActualRecordDto {
    message: string;
    kpiPerticularAttempted: string | null;
}
export declare class KpiPlanActualProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedKpiPlanActualRecordDto[];
}
export declare class KpiPlanActualApiResponseDto extends BaseApiResponseDto {
    data: KpiPlanActualProcessedDataDto;
}
