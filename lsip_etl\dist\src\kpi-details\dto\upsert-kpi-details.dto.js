"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertKpiDetailsDto = exports.KpiDetailRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class KpiDetailRecordDto {
    id;
    name;
    currencyIsoCode;
    companyId;
    aovRs;
    available;
    averageOrderValueProduct;
    averageOrderValue;
    businessUnitId;
    cacRs;
    cac;
    cm2;
    cm3OfNmv;
    cmInLocalitiesWith500Users;
    cumulativeBankAccounts;
    bankAccountsCreatedPerMonth;
    cashInBank;
    categoryId;
    contributionMarginProduct;
    averageDepositBankAccount;
    contributionMargin;
    numberOfCreditCardIssued;
    customerAcquisitonCost;
    dau;
    dtrFromSource;
    date;
    cashBurnRsCr;
    cashBurn;
    ebitdaRsCr;
    ebitda;
    enable;
    firstPartyMix;
    firstParty;
    fulfillmentCostOfNmv;
    gmv;
    gmvFromPrivateLabels;
    gmvPerExistingBuyer;
    gmvPerFosCost;
    gmvPerNewBuyer;
    contributionMarginRsCr;
    grossMargin;
    grossMarginOfNmv;
    inventoryDays;
    latestFlag;
    m12GmvRetention;
    m12UserRetention;
    m1Repeat;
    m2GmvRetention;
    contributionMarginOfTakeRate;
    m2UserRetention;
    m6GmvRetention;
    m6UserRetention;
    discretionaryMarketing;
    grossMarginProduct;
    mau;
    marketingSpends;
    m2Retention;
    marketplaceFloat;
    merchantAcquisitionCost;
    nmv;
    nmvPerBuyerRs;
    noOfActiveTrucks;
    noOfDenseLanes;
    noOfDenseLocalities;
    noOfOrders;
    noOfPayingMerchants;
    noOfTrackedTrips;
    noOfTransactingBuyers;
    noOfTransactingUsers;
    noOfTransactions;
    macRs;
    numberOfBuyersNumber;
    numberOfLocalitiesWith500Users;
    numberOfNewUsers;
    numberOfTransactingUsersLakh;
    operatingCashBurnRsCr;
    orderFrequency;
    payableDays;
    payingMerchants;
    peopleGAOthersRsCr;
    mauLakh;
    marketing;
    receivableDays;
    recordTypeName;
    revenuetotal;
    salesCost;
    shippingCost;
    shippingCostOfNmv;
    takeRateGmv;
    takeRateRsCr;
    takeRate;
    takeRateByRevenueProduct;
    takeRateByRevenue;
    takeRateFromLocalitiesWith500User;
    takeRatePerUserRs;
    takeRateRevenueFromDenseLocalitie;
    takeRateRevenuePerExistingUser;
    takeRateRevenuePerNewUser;
    testKpi;
    totalPeopleCost;
    transactingBuyers;
    transactingStoresPerFos;
    transactingUserInLocalitiesWith500;
    typeOfKpi;
    workingCapital;
    key;
    numberOfBuyers;
    revenueRsCr;
    cm1;
    customerSupportBizOpsCost;
    underUtilizedFixedCostsInOperations;
    techProductCosts;
    sgA;
    buyerWithCredit;
    yim12MonthsAfterDpd;
    performanceMarketing;
    cmRsCr;
    takeRateOfLocalitiesWith500Users;
    performanceMarketingRsCr;
    cmInLocalities500UsersRsCr;
    payingUsers;
    m3PayingUserRetention;
    m3PayingRevenueRetention;
    numberOfAudioHeardPerDau;
    moArpu;
    totalArpu;
    cacDollar;
    m0Conversion;
    conversion;
    subscritpionIncome;
    marketingCost;
    employeeExpenseIndiaUs;
    ebitdaDollar;
    totalCashburn;
    nmvUsd;
    fmcgNmv;
    temanUlaNmv;
    othersNmv;
    takeRateNetMargin;
    takeRateFmcg;
    takeRateTermanUla;
    logisticsCost;
    fos;
    warehouse;
    cashInBankDollar;
    uniqueOrderingStores;
    m6StoreRetention;
    nmvPerStorePerMonth;
    aovDollar;
    ordersPerStorePerMonth;
    appRunningCostServerAnalyticsCos;
    marketingCostPercent;
    contentServingCost;
    otherBrandingExpenses;
    musicLicenseCost;
    employeeBenefitsExpenses;
    others;
    dauMau;
    retentionD30;
    averageTimeSpent;
    uniqueCreators;
    totalInstalls;
    organic;
    costPerInstall;
    revenueDollar;
    skilling;
    recruitment;
    marketingOthers;
    salary;
    newInstalls;
    profileComplete;
    uninstallProfileCompletes;
    screenedLeads;
    fromExisting;
    fromReactivated;
    fromNew;
    screenedLeadsMau;
    m3UserRetention;
    repeatMauToLifeToDateUserBase;
    blendedCacForUser;
    newEmployerAcquired;
    activeEmployer;
    jobsActivated;
    m3EmployerRetention;
    repeatEmployerToLifeToDateEmployer;
    blendedCacEmployer;
    communityMauOfAppMau;
    communityToCommunityM1D30D60Rete;
    overallEnquiriesReceivedMtdYtd;
    conversionRateTrailing3Months;
    newOrdersForTheMonth;
    gtvArrOnSaas;
    gmvDollar;
    takeRateDollar;
    deliveredGmvForTheMonth;
    cumulativeOpenOrderBookClosing;
    cmPercent;
    numberOfLspsShippersAdded;
    activeShippersLsps;
    fixedExpenses;
    gmvFromRepeatCustomers;
    newCustomersAcquired;
    activeCustomers;
    q3CustomerRetention;
    activeSuppliers;
    q3SupplierRetention;
    arValueInRsCrs;
    apValueInRsCrs;
    inventoryValueInRsCrs;
    netWcArApInvAdv;
    wcConversionDays;
    freeCash;
    endingCarr;
    newCarrUpsell;
    monthlyChurnDowngrade;
    noOfCustomersCarr;
    carrPerCustomer;
    endingArr;
    newArrUpsell;
    monthlyChurnDowngradeArr;
    numberOfCustomersArr;
    arrPerCustomer;
    arrAsAOfCarr;
    platform;
    cpaas;
    voice;
    services;
    grossProfit;
    operatingIncome;
    dso;
    netRetention;
    averageCarrPerNewCustomer;
    carrFte;
    arrFte;
    annualisedNetChurn;
    ruleOf40;
    netMagicNumber;
    salesCost000;
    netRevenue;
    otherRevenueEGShippingRevenueEtc;
    cogs;
    allDiscountsRefundsEtc;
    wastage;
    parkingFuelLastMile;
    packagingCost;
    hubOperationsCost;
    cm2PercentageOfNetrevenue;
    marketingCostPercentageOfNetrevenue;
    cm3PercentOfNetRevenue;
    numberOfOrders;
    existingBuyers;
    ordersPerBuyer;
    existingBuyersNoBuyerPermonth;
    noOfUniqueBuyers;
    existingBuyers000;
    gmvFromExistingBuyers;
    m3GmvRetention;
    gmvFromTop3Categories;
    grossBooking;
    newExpansion;
    downgradeChurn;
    ndr;
    salesMarketing;
    generalAdministrative;
    researchDevelopment;
    netBurn000;
    burnMultiple;
    avgNewDealSize;
    eventCreated;
    eventHosted;
    eventsWithLessThan500Participants;
    eventsWith500To1000Participants;
    eventsWithGreater1000Participants;
    roiOnMarketingSpends;
    roiOnSalesSpends;
    salesQuotaAttainment;
    peopleRelatedSpend;
    cashBurnDollar000;
    netDollarRetention;
    grossRetention;
    netSeatRetention;
    grossSeatRetention;
    logoRetention;
    totalSMSpend;
    sMEfficiency;
    newLogoArr;
    newLogoSeats;
    newLogo;
    newLogoAsp;
    newLogoAvgSeats;
    ebitdaBurn;
    totalBookings;
    global;
    smb;
    mm;
    enterprise;
    invoicedRevenue;
    gm;
    smbPercent;
    mmPercent;
    enterprisePercent;
    mlToMql;
    mqlToSql;
    sqlToClosed;
    grossRoi;
    outboundQuotaAttainment;
    enterpriseMmCac;
    smbInboundMarketingCac;
    saasLogoQ1Retention;
    saasRevenueQ1Retention;
    saasConversionSelfServeFunnel;
    marketplaceEnterpriseLogoQ1Reten;
    marketplaceEnterpriseRevenueQ1Re;
    marketplaceMmLogoQ1Retention;
    marketplaceMmRevenueQ1Retention;
    creatorEarnings;
    editorEarnings;
    cm1Percent;
    inboundQuotaAttainment;
    m3SpendPerCustomer;
    m3M0SpendPerCustomer;
    npas;
    moneyRemittedToIndiaOnZolve;
    discretionaryMarketingRsCr;
    takeRateOthers;
    subsRevenue;
    subsHostingCost;
    subsClientSupportCost;
    arrCarr;
    iarrContracted;
    employeesOnPlatformContracted;
    averageAcv;
    tradingVolume;
    tradingRevenue;
    cumulativeDownloads;
    cumulativeKycedUsers;
    numberOfActiveTraders;
    traderW24Retention;
    averageTradingFrequencyPerTrader;
    totalContractsValueArr;
    totalTerminalArr;
    terminalArrNetOfDiscounts;
    terminalArrAdded;
    terminalArrLost;
    nonRecurringRevenue;
    totalInstitutes;
    numberOfNewInstitutes;
    terminalArrInstitute;
    sMEfficiencyNetOfDiscounts;
    totalCreators;
    numberOfNewCreators;
    totalCustomers;
    terminalArrLostDollar;
    q2TripRetention;
    m6LspRetenAdjustedForPilotTrail;
    m6ShipperRetentionAdjustedTrials;
    numberOfSuppliersAdded;
    aumTotal;
    grossProfitPercent;
    aumCash;
    aumCpf;
    aumSrs;
    newInvestment;
    redemption;
    netNewMoneyInvested;
    existingClients;
    newClients;
    registeredAccounts;
    brokerAccounts;
    fundedClients;
    churnedClientsFromCompany;
    shareOfPeerReferrals;
    recurringAmount;
    gmClient;
    paybackPeriodOnCac;
    m6MedianNetInvested;
    cmPercentRevenue;
    expansionArr;
    expansionArrOverallArr;
    headcount;
    grossMarginDollar;
    income;
    arrCac;
    churn;
    ltvCac;
}
exports.KpiDetailRecordDto = KpiDetailRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Record ID", maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Port Co KPI Name", maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Currency ISO Code", maxLength: 3 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(3),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "currencyIsoCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Company", maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(18),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "companyId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "AOV (Rs.)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "aovRs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Available?" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], KpiDetailRecordDto.prototype, "available", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Average Order Value Product" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "averageOrderValueProduct", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Average Order Value" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "averageOrderValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Business Unit", maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(18),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "businessUnitId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CAC (Rs.)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cacRs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CAC" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cac", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM2 (% of NMV)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cm2", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM3 (% of NMV)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cm3OfNmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM % in localities with >500 users" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cmInLocalitiesWith500Users", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cumulative bank accounts" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cumulativeBankAccounts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Bank accounts created per month" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "bankAccountsCreatedPerMonth", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cash in Bank" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cashInBank", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Category", maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(18),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Contribution Margin Product" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "contributionMarginProduct", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Average deposit / bank account" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "averageDepositBankAccount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Contribution Margin" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "contributionMargin", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "# of Credit Card issued" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfCreditCardIssued", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Customer Acquisiton Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "customerAcquisitonCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "DAU" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "dau", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "DTR from source" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "dtrFromSource", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Date" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cash Burn (Rs. Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cashBurnRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cash Burn" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cashBurn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "EBITDA (Rs.Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "ebitdaRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "EBITDA" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "ebitda", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Enable?" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], KpiDetailRecordDto.prototype, "enable", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "First Party Mix (%)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "firstPartyMix", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "First_Party" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "firstParty", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Fulfillment cost (% of NMV)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "fulfillmentCostOfNmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GMV" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GMV from private labels" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmvFromPrivateLabels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GMV per EXISTING buyer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmvPerExistingBuyer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GMV per FOS cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmvPerFosCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GMV per NEW buyer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmvPerNewBuyer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Contribution Margin (Rs. Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "contributionMarginRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross Margin" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossMargin", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross Margin (% of NMV)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossMarginOfNmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Inventory days" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "inventoryDays", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Latest Flag" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], KpiDetailRecordDto.prototype, "latestFlag", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M12 GMV retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m12GmvRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M12 user retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m12UserRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M1 Repeat (%)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m1Repeat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M2 GMV retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m2GmvRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Contribution Margin (% of take rate)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "contributionMarginOfTakeRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M2 user retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m2UserRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M6 GMV retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m6GmvRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M6 user retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m6UserRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Discretionary marketing" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "discretionaryMarketing", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross Margin Product" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossMarginProduct", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "MAU" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "mau", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketing Spends" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketingSpends", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M2 retention (%)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m2Retention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketplace float" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketplaceFloat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Merchant Acquisition Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "merchantAcquisitionCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "NMV(Rs.Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "nmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "NMV per buyer (Rs.)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "nmvPerBuyerRs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of active trucks" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfActiveTrucks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of dense lanes" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfDenseLanes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of dense localities" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfDenseLocalities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of orders" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfOrders", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of paying merchants" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfPayingMerchants", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of tracked trips" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfTrackedTrips", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of transacting buyers" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfTransactingBuyers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of transacting users" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfTransactingUsers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of transactions" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfTransactions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "MAC (Rs.)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "macRs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Number of buyers (number)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfBuyersNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Number of localities with >500 users" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfLocalitiesWith500Users", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Number of new users ('000)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfNewUsers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Number of transacting users (Lakh)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfTransactingUsersLakh", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Operating Cash Burn (Rs.Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "operatingCashBurnRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Order Frequency" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "orderFrequency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Payable days" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "payableDays", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Paying Merchants (Lakhs)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "payingMerchants", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "People + G&A + Others (Rs. Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "peopleGAOthersRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "MAU (Lakh)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "mauLakh", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketing" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketing", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Receivable days" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "receivableDays", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Record Type Name", maxLength: 1300 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1300),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "recordTypeName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "RevenueTotal" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "revenuetotal", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Sales Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "salesCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Shipping cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "shippingCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Shipping cost (% of NMV)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "shippingCostOfNmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate GMV" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateGmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate (Rs. Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate by Revenue Product" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateByRevenueProduct", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate by Revenue" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateByRevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Takerate in localities with >500users(%)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateFromLocalitiesWith500User", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take rate per user (Rs.)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRatePerUserRs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take rate revenue from dense localitie" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateRevenueFromDenseLocalitie", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take rate revenue per existing user" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateRevenuePerExistingUser", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take rate revenue per new user" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateRevenuePerNewUser", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Test KPI" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "testKpi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total People Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalPeopleCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Transacting Buyers" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "transactingBuyers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Transacting stores per FOS" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "transactingStoresPerFos", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Transacting user in localities with >500" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "transactingUserInLocalitiesWith500", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Type of KPI", maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "typeOfKpi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Working Capital" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "workingCapital", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Key", maxLength: 100 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], KpiDetailRecordDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Number of buyers (number)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfBuyers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Revenue(Rs.Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "revenueRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM1 (% of NMV)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cm1", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Customer Support & Biz Ops Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "customerSupportBizOpsCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Under-utilized fixed costs in operations" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "underUtilizedFixedCostsInOperations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Tech & Product Costs" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "techProductCosts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "SG&A" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "sgA", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "% buyer with credit" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "buyerWithCredit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "% YIM (12 months after DPD)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "yim12MonthsAfterDpd", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Performance marketing" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "performanceMarketing", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM (Rs. Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cmRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "takeRate of localities> 500 users(RS))" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateOfLocalitiesWith500Users", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Performance marketing (Rs. Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "performanceMarketingRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM % in localities >500 users(Rs. cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cmInLocalities500UsersRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Paying users" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "payingUsers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "m3 paying user retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m3PayingUserRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "m3 paying revenue retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m3PayingRevenueRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "# of audio heard per DAU" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfAudioHeardPerDau", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "MO ARPU" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "moArpu", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total ARPU" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalArpu", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CAC ( $)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cacDollar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M0 Conversion (%)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m0Conversion", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Conversion (%)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "conversion", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Subscritpion income ($)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "subscritpionIncome", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketing cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketingCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Employee Expense (India + US)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "employeeExpenseIndiaUs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "EBITDA ( $ )" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "ebitdaDollar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total Cashburn ( $)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalCashburn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "NMV (USD)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "nmvUsd", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "FMCG_NMV _%" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "fmcgNmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Teman Ula NMV %" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "temanUlaNmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Others NMV %" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "othersNmv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate Net Margin %" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateNetMargin", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate FMCG %" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateFmcg", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate Terman Ula %" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateTermanUla", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Logistics Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "logisticsCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "FoS" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "fos", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Warehouse" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "warehouse", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cash in Bank ($)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cashInBankDollar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Unique Ordering Stores" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "uniqueOrderingStores", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M6 Store retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m6StoreRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "NMV per store per month" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "nmvPerStorePerMonth", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "AOV ($)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "aovDollar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Orders per store per month" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "ordersPerStorePerMonth", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "App Running Cost (Server & Analytics )" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "appRunningCostServerAnalyticsCos", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketing Cost (%)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketingCostPercent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Content Serving Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "contentServingCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Other Branding Expenses" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "otherBrandingExpenses", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Music License Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "musicLicenseCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Employee Benefits Expenses" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "employeeBenefitsExpenses", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Others" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "others", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "DAU / MAU" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "dauMau", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Retention D30" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "retentionD30", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Average time spent" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "averageTimeSpent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Unique Creators(mn)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "uniqueCreators", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total Installs (mn)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalInstalls", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "% Organic" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "organic", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cost Per Install" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "costPerInstall", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Revenue" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "revenueDollar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Skilling" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "skilling", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Recruitment" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "recruitment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketing + Others" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketingOthers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Salary" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "salary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New Installs" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newInstalls", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Profile Complete" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "profileComplete", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Uninstall profile completes" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "uninstallProfileCompletes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Screened Leads" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "screenedLeads", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "From existing" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "fromExisting", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "From reactivated" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "fromReactivated", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "From New" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "fromNew", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Screened Leads / MAU" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "screenedLeadsMau", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M3 user retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m3UserRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Repeat MAU to Life to date User Base" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "repeatMauToLifeToDateUserBase", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Blended CAC for user" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "blendedCacForUser", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New employer acquired" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newEmployerAcquired", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Active employer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "activeEmployer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Jobs activated" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "jobsActivated", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M3 Employer retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m3EmployerRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Repeat Employer to Life to date Employer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "repeatEmployerToLifeToDateEmployer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Blended CAC Employer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "blendedCacEmployer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "% Community MAU of App MAU" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "communityMauOfAppMau", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Community to Community M1 (D30-D60) Rete" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "communityToCommunityM1D30D60Rete", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Overall Enquiries Received (MTD & YTD )" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "overallEnquiriesReceivedMtdYtd", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Conversion rate (trailing 3 months)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "conversionRateTrailing3Months", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New Orders for the month" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newOrdersForTheMonth", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GTV ARR ON SAAS" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gtvArrOnSaas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GMV" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmvDollar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateDollar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Delivered GMV for the month" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "deliveredGmvForTheMonth", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cumulative Open Order Book (Closing)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cumulativeOpenOrderBookClosing", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cmPercent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Number of LSPs + Shippers added" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfLspsShippersAdded", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Active shippers + LSPs" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "activeShippersLsps", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Fixed Expenses" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "fixedExpenses", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GMV from Repeat Customers (%)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmvFromRepeatCustomers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New Customers acquired" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newCustomersAcquired", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Active Customers" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "activeCustomers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Q3 customer retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "q3CustomerRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Active Suppliers" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "activeSuppliers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Q3 supplier retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "q3SupplierRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "AR: (Value in Rs Crs)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "arValueInRsCrs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "AP: (Value in Rs Crs)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "apValueInRsCrs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Inventory: (Value in Rs Crs)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "inventoryValueInRsCrs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Net WC (AR-AP+Inv+Adv)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "netWcArApInvAdv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "WC Conversion Days" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "wcConversionDays", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Free Cash" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "freeCash", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Ending CARR" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "endingCarr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New CARR + Upsell" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newCarrUpsell", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Monthly Churn/Downgrade" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "monthlyChurnDowngrade", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "No of customers (CARR)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfCustomersCarr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CARR per customer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "carrPerCustomer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Ending ARR" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "endingArr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New ARR + Upsell" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newArrUpsell", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Monthly Churn/Downgrade(ARR)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "monthlyChurnDowngradeArr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Number of customers (ARR)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfCustomersArr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "ARR per customer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "arrPerCustomer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "ARR as a % of CARR" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "arrAsAOfCarr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Platform" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "platform", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CPaaS" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cpaas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Voice" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "voice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Services" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "services", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross Profit" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossProfit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Operating Income" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "operatingIncome", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "DSO" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "dso", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Net retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "netRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Average CARR per new customer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "averageCarrPerNewCustomer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CARR/FTE" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "carrFte", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "ARR/FTE" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "arrFte", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Annualised net churn" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "annualisedNetChurn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Rule of 40" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "ruleOf40", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Net Magic Number" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "netMagicNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Sales cost ($000)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "salesCost000", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Net Revenue" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "netRevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Other revenue (e.g.shipping revenue etc)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "otherRevenueEGShippingRevenueEtc", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "COGS" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cogs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "All discounts / refunds etc." }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "allDiscountsRefundsEtc", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Wastage" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "wastage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Parking + Fuel (=last mile)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "parkingFuelLastMile", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Packaging cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "packagingCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Hub operations cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "hubOperationsCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM2" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cm2PercentageOfNetrevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketing cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketingCostPercentageOfNetrevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM3" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cm3PercentOfNetRevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "# of orders" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfOrders", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Existing buyers" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "existingBuyers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Orders per buyer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "ordersPerBuyer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Existing buyers (No of buyer per month)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "existingBuyersNoBuyerPermonth", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "# of unique buyers" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "noOfUniqueBuyers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Existing buyers(#'000)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "existingBuyers000", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "% GMV from existing buyers" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmvFromExistingBuyers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M3 GMV retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m3GmvRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "% GMV from top 3 categories" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmvFromTop3Categories", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross Booking" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossBooking", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New + Expansion" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newExpansion", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Downgrade + Churn" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "downgradeChurn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "NDR" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "ndr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "(-) Sales & Marketing" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "salesMarketing", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "(-) General & Administrative" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "generalAdministrative", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "(-) Research & Development" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "researchDevelopment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Net Burn $000" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "netBurn000", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Burn multiple" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "burnMultiple", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Avg. new deal size" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "avgNewDealSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Event Created" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "eventCreated", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Event Hosted" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "eventHosted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "% events with <500 participants" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "eventsWithLessThan500Participants", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "% events with 500-1000 participants" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "eventsWith500To1000Participants", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "% events with >1000 participants" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "eventsWithGreater1000Participants", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "ROI on marketing spends" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "roiOnMarketingSpends", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "ROI on sales spends" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "roiOnSalesSpends", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Sales Quota Attainment" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "salesQuotaAttainment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "People Related Spend" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "peopleRelatedSpend", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cash Burn ($000)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cashBurnDollar000", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Net dollar retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "netDollarRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross $Retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Net seat Retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "netSeatRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross Seat Retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossSeatRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Logo Retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "logoRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total S&M Spend" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalSMSpend", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "S&M Efficiency" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "sMEfficiency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New Logo ARR" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newLogoArr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New Logo Seats" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newLogoSeats", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New Logo" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newLogo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New Logo ASP" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newLogoAsp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New Logo Avg Seats" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newLogoAvgSeats", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "EBITDA burn" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "ebitdaBurn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total bookings" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalBookings", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Global" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "global", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "SMB" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "smb", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "MM" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "mm", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Enterprise" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "enterprise", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Invoiced Revenue" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "invoicedRevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GM" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gm", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "SMB" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "smbPercent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "MM" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "mmPercent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Enterprise" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "enterprisePercent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "ML to MQL" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "mlToMql", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "MQL to SQL" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "mqlToSql", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "SQL to Closed" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "sqlToClosed", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross ROI" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossRoi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Outbound quota attainment" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "outboundQuotaAttainment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Enterprise + MM CAC" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "enterpriseMmCac", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "SMB inbound+marketing CAC" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "smbInboundMarketingCac", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "SAAS - logo Q1 retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "saasLogoQ1Retention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "SAAS - revenue Q1 retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "saasRevenueQ1Retention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "SAAS - conversion, self serve funnel" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "saasConversionSelfServeFunnel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketplace - Enterprise - logo Q1 reten" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketplaceEnterpriseLogoQ1Reten", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketplace - Enterprise - revenue Q1 re" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketplaceEnterpriseRevenueQ1Re", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketplace - MM - logo Q1 retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketplaceMmLogoQ1Retention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Marketplace - MM - revenue Q1 retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "marketplaceMmRevenueQ1Retention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Creator Earnings" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "creatorEarnings", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Editor Earnings" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "editorEarnings", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "CM1" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cm1Percent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Inbound quota attainment" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "inboundQuotaAttainment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M3 Spend per customer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m3SpendPerCustomer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M3 / M0 spend per customer" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m3M0SpendPerCustomer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "NPAs" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "npas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Money remitted to India on Zolve" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "moneyRemittedToIndiaOnZolve", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Discretionary marketing (Rs. Cr)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "discretionaryMarketingRsCr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Take Rate Others %" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "takeRateOthers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Subs Revenue" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "subsRevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Subs - Hosting Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "subsHostingCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Subs - Client Support Cost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "subsClientSupportCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "ARR/CARR" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "arrCarr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "IARR (contracted)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "iarrContracted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Employees on platform (Contracted)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "employeesOnPlatformContracted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Average ACV" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "averageAcv", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Trading volume" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "tradingVolume", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Trading Revenue" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "tradingRevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cumulative downloads" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cumulativeDownloads", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Cumulative KYCed users" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cumulativeKycedUsers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "# of active traders" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfActiveTraders", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Trader W24 retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "traderW24Retention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Average Trading Frequency per Trader" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "averageTradingFrequencyPerTrader", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total Contracts Value (ARR)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalContractsValueArr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Terminal ARR" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalTerminalArr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Terminal ARR net of discounts" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "terminalArrNetOfDiscounts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Terminal ARR added" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "terminalArrAdded", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Terminal ARR lost" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "terminalArrLost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Non recurring revenue" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "nonRecurringRevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total institutes" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalInstitutes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "# of new institutes" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfNewInstitutes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Terminal ARR / institute" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "terminalArrInstitute", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "S&M efficiency net of discounts" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "sMEfficiencyNetOfDiscounts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total creators" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalCreators", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "# of new creators" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfNewCreators", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total customers" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "totalCustomers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Terminal ARR lost($)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "terminalArrLostDollar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Q2 trip retention" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "q2TripRetention", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M6 LSP reten. adjusted for pilot / trail" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m6LspRetenAdjustedForPilotTrail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M6 Shipper retention adjusted / trials" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m6ShipperRetentionAdjustedTrials", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Number of suppliers added" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "numberOfSuppliersAdded", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "AUM Total" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "aumTotal", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross Profit" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossProfitPercent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "AUM Cash" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "aumCash", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "AUM CPF" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "aumCpf", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "AUM SRS" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "aumSrs", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New investment" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newInvestment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Redemption" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "redemption", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Net new money invested" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "netNewMoneyInvested", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Existing Clients" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "existingClients", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "New Clients" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "newClients", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Registered accounts" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "registeredAccounts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Broker accounts" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "brokerAccounts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Funded clients" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "fundedClients", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Churned clients (from company)" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "churnedClientsFromCompany", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Share of peer referrals" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "shareOfPeerReferrals", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Recurring amount" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "recurringAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "GM / client" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "gmClient", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Payback period on CAC" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "paybackPeriodOnCac", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "M6 median net invested" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "m6MedianNetInvested", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "cm" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "cmPercentRevenue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Expansion ARR" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "expansionArr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Expansion ARR/Overall ARR" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "expansionArrOverallArr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Headcount" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "headcount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Gross Margin" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "grossMarginDollar", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Income" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "income", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "ARR:CAC" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "arrCac", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Churn %" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "churn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "LTV:CAC" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiDetailRecordDto.prototype, "ltvCac", void 0);
class UpsertKpiDetailsDto {
    KpiDetails;
}
exports.UpsertKpiDetailsDto = UpsertKpiDetailsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [KpiDetailRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => KpiDetailRecordDto),
    __metadata("design:type", Array)
], UpsertKpiDetailsDto.prototype, "KpiDetails", void 0);
//# sourceMappingURL=upsert-kpi-details.dto.js.map