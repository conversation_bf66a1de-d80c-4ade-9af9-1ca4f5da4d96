"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncedRecordsQueryDto = exports.SyncedRecordsResponseDto = exports.SyncedRecordsDataDto = exports.SyncedDatasetDto = exports.RecordsSummaryResponseDto = exports.RecordsSummaryDataDto = exports.DatasetSummaryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
class DatasetSummaryDto {
    key;
    label;
    count;
}
exports.DatasetSummaryDto = DatasetSummaryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'accounts' }),
    __metadata("design:type", String)
], DatasetSummaryDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Accounts' }),
    __metadata("design:type", String)
], DatasetSummaryDto.prototype, "label", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 125 }),
    __metadata("design:type", Number)
], DatasetSummaryDto.prototype, "count", void 0);
class RecordsSummaryDataDto {
    totalRecords;
    datasets;
}
exports.RecordsSummaryDataDto = RecordsSummaryDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1250 }),
    __metadata("design:type", Number)
], RecordsSummaryDataDto.prototype, "totalRecords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [DatasetSummaryDto] }),
    __metadata("design:type", Array)
], RecordsSummaryDataDto.prototype, "datasets", void 0);
class RecordsSummaryResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.RecordsSummaryResponseDto = RecordsSummaryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: RecordsSummaryDataDto }),
    __metadata("design:type", RecordsSummaryDataDto)
], RecordsSummaryResponseDto.prototype, "data", void 0);
class SyncedDatasetDto extends DatasetSummaryDto {
    synced;
}
exports.SyncedDatasetDto = SyncedDatasetDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    __metadata("design:type", Boolean)
], SyncedDatasetDto.prototype, "synced", void 0);
class SyncedRecordsDataDto {
    totalTrackedDatasets;
    totalDatasetsWithRecords;
    datasets;
}
exports.SyncedRecordsDataDto = SyncedRecordsDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 15 }),
    __metadata("design:type", Number)
], SyncedRecordsDataDto.prototype, "totalTrackedDatasets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 12 }),
    __metadata("design:type", Number)
], SyncedRecordsDataDto.prototype, "totalDatasetsWithRecords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [SyncedDatasetDto] }),
    __metadata("design:type", Array)
], SyncedRecordsDataDto.prototype, "datasets", void 0);
class SyncedRecordsResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.SyncedRecordsResponseDto = SyncedRecordsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: SyncedRecordsDataDto }),
    __metadata("design:type", SyncedRecordsDataDto)
], SyncedRecordsResponseDto.prototype, "data", void 0);
class SyncedRecordsQueryDto {
    includeZero;
}
exports.SyncedRecordsQueryDto = SyncedRecordsQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include datasets that currently have zero records.',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBooleanString)(),
    __metadata("design:type", String)
], SyncedRecordsQueryDto.prototype, "includeZero", void 0);
//# sourceMappingURL=records-summary.dto.js.map