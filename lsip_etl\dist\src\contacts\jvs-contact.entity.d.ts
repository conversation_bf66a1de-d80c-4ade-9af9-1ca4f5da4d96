export declare class JvsContact {
    id: string;
    isDeleted: boolean | null;
    masterRecordId: string | null;
    accountId: string | null;
    lastName: string | null;
    firstName: string | null;
    salutation: string | null;
    middleName: string | null;
    name: string | null;
    phone: string | null;
    fax: string | null;
    mobilePhone: string | null;
    assistantPhone: string | null;
    reportsToId: string | null;
    email: string | null;
    title: string | null;
    department: string | null;
    assistantName: string | null;
    birthDate: Date | null;
    hasOptedOutOfEmail: boolean | null;
    hasOptedOutOfFax: boolean | null;
    doNotCall: boolean | null;
    titleType: string | null;
    departmentGroup: string | null;
    companyFundId: string | null;
    dayOfMonth: number | null;
    designationRank: number | null;
    dateOfBirth: Date | null;
    director: boolean | null;
    department2: string | null;
    designation: string | null;
    keyContact: boolean | null;
    rating: string | null;
}
