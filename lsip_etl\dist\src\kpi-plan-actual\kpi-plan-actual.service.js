"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiPlanActualService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_kpi_plan_actual_entity_1 = require("./jvs-kpi-plan-actual.entity");
const jvs_kpi_perticular_entity_1 = require("../kpi-perticular/jvs-kpi-perticular.entity");
let KpiPlanActualService = class KpiPlanActualService {
    kpiPlanActualRepository;
    kpiPerticularRepository;
    constructor(kpiPlanActualRepository, kpiPerticularRepository) {
        this.kpiPlanActualRepository = kpiPlanActualRepository;
        this.kpiPerticularRepository = kpiPerticularRepository;
    }
    async upsertKpiPlanActuals(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedKpiPerticularIds = new Set(records.map((record) => record.kpi_particular__c).filter((value) => Boolean(value)));
        const existingKpiPerticularIds = requestedKpiPerticularIds.size
            ? new Set((await this.kpiPerticularRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedKpiPerticularIds]) },
            })).map((particular) => particular.id))
            : new Set();
        const entities = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                kpiPerticularAttempted: null,
            };
            const kpiPerticularId = record.kpi_particular__c && existingKpiPerticularIds.has(record.kpi_particular__c)
                ? record.kpi_particular__c
                : null;
            if (record.kpi_particular__c && !kpiPerticularId) {
                meta.kpiPerticularAttempted = record.kpi_particular__c;
                meta.issues.push(`KPI particular ${record.kpi_particular__c} not found; stored without KPI particular linkage.`);
            }
            const entity = this.kpiPlanActualRepository.create({
                id: record.id,
                name: record.name ?? null,
                actual: record.actual__c ?? null,
                kpiPerticularId,
                month1: record.month1__c ?? null,
                alternateName: record.name__c ?? null,
                numberOfPlanRevisions: record.number_of_plan_revisions__c ?? null,
                plan: record.plan__c ?? null,
                quarter: record.quarter__c ?? null,
                reasonForDeviationRemarks: record.reason_for_deviation_remarks__c ?? null,
                year: record.year__c ?? null,
                actualFormula: record.actual_formula__c ?? null,
                monthNumber: record.month_number__c ?? null,
                percentageOfAchievement: record.percentage_of_achievement__c ?? null,
                consolidatedDate: record.consolidated_date__c ?? null,
                planActualDate: record.plan_actual_date__c ? new Date(record.plan_actual_date__c) : null,
            });
            metaById.set(record.id, meta);
            entities.push(entity);
        }
        if (entities.length > 0) {
            await this.kpiPlanActualRepository.save(entities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    kpiPerticularAttempted: meta.kpiPerticularAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.KpiPlanActualService = KpiPlanActualService;
exports.KpiPlanActualService = KpiPlanActualService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_kpi_plan_actual_entity_1.JvsKpiPlanActual)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_kpi_perticular_entity_1.JvsKpiPerticular)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], KpiPlanActualService);
//# sourceMappingURL=kpi-plan-actual.service.js.map