"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsKpiRelevancy = void 0;
const typeorm_1 = require("typeorm");
let JvsKpiRelevancy = class JvsKpiRelevancy {
    id;
    name = null;
    currencyIsoCode = null;
    companyId = null;
    industryMasterId = null;
    kpiMasterId = null;
    subIndustryId = null;
    benchmarkId = null;
};
exports.JvsKpiRelevancy = JvsKpiRelevancy;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsKpiRelevancy.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiRelevancy.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'currencyisocode', type: 'varchar', length: 3, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiRelevancy.prototype, "currencyIsoCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiRelevancy.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'industry_master__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiRelevancy.prototype, "industryMasterId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kpi_master__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiRelevancy.prototype, "kpiMasterId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sub_industry__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiRelevancy.prototype, "subIndustryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'benchmark__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiRelevancy.prototype, "benchmarkId", void 0);
exports.JvsKpiRelevancy = JvsKpiRelevancy = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_kpi_relevancy' })
], JvsKpiRelevancy);
//# sourceMappingURL=jvs-kpi-relevancy.entity.js.map