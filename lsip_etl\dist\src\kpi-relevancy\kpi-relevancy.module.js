"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiRelevancyModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const kpi_relevancy_controller_1 = require("./kpi-relevancy.controller");
const kpi_relevancy_service_1 = require("./kpi-relevancy.service");
const jvs_kpi_relevancy_entity_1 = require("./jvs-kpi-relevancy.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
const jvs_kpi_master_entity_1 = require("../kpi-master/jvs-kpi-master.entity");
const jvs_industry_entity_1 = require("../industries/jvs-industry.entity");
let KpiRelevancyModule = class KpiRelevancyModule {
};
exports.KpiRelevancyModule = KpiRelevancyModule;
exports.KpiRelevancyModule = KpiRelevancyModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_kpi_relevancy_entity_1.JvsKpiRelevancy, jvs_account_entity_1.JvsAccount, jvs_kpi_master_entity_1.JvsKpiMaster, jvs_industry_entity_1.JvsIndustry])],
        controllers: [kpi_relevancy_controller_1.KpiRelevancyController],
        providers: [kpi_relevancy_service_1.KpiRelevancyService],
        exports: [kpi_relevancy_service_1.KpiRelevancyService],
    })
], KpiRelevancyModule);
//# sourceMappingURL=kpi-relevancy.module.js.map