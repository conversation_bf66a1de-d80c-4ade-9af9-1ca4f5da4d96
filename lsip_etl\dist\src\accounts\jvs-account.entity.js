"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsAccount = void 0;
const typeorm_1 = require("typeorm");
let JvsAccount = class JvsAccount {
    id;
    masterRecordId = null;
    name = null;
    type = null;
    parentId = null;
    phone = null;
    website = null;
    industry = null;
    description = null;
    currencyIsoCode = null;
    isPartner = null;
    boardPositionHeld = null;
    companyFullName = null;
    goingInCost = null;
    goingInPostMoneyValue = null;
    goingInPreMoney = null;
    hqGeography = null;
    initialInvestmentStage = null;
    initialOwnership = null;
    initialRevenueStage = null;
    initialRound = null;
    lsipInitialFinancingRound = null;
    lead = null;
    legalEntityName = null;
    legalGeography = null;
    sector = null;
    shortName = null;
    secondLead = null;
    documentation = null;
    companyStatus = null;
    region = null;
    currencyFormat = null;
    industryMasterId = null;
    subIndustryId = null;
    regionCountry = null;
    auditor = null;
    benchmark = null;
    entity = null;
    business = null;
    year = null;
};
exports.JvsAccount = JvsAccount;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsAccount.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'masterrecordid', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "masterRecordId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'parentid', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "website", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "industry", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 32000, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'currencyisocode', type: 'varchar', length: 3, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "currencyIsoCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ispartner', type: 'boolean', nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "isPartner", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'board_position_held__c', type: 'varchar', length: 40, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "boardPositionHeld", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company_full_name__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "companyFullName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'going_in_cost__c', type: 'text', nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "goingInCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'going_in_post_money_value__c', type: 'text', nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "goingInPostMoneyValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'going_in_pre_money__c', type: 'text', nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "goingInPreMoney", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'hq_geography__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "hqGeography", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initial_investment_stage__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "initialInvestmentStage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initial_ownership__c', type: 'text', nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "initialOwnership", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initial_revenue_stage__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "initialRevenueStage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initial_round__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "initialRound", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lsip_initial_financing_roung__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "lsipInitialFinancingRound", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lead__c', type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "lead", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'legal_entity_name__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "legalEntityName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'legal_geography__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "legalGeography", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sector__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "sector", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'short_name__c', type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "shortName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'x2nd_lead__c', type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "secondLead", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'documentation__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "documentation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company_status__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "companyStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'region__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "region", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'currency_format__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "currencyFormat", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'industry_master__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "industryMasterId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sub_industry__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "subIndustryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'region_country__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "regionCountry", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'auditor__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "auditor", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'benchmark__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "benchmark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'entity__c', type: 'varchar', length: 4099, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "entity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'business__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "business", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'year__c', type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsAccount.prototype, "year", void 0);
exports.JvsAccount = JvsAccount = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_accounts' })
], JvsAccount);
//# sourceMappingURL=jvs-account.entity.js.map