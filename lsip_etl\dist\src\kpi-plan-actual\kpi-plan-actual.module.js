"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiPlanActualModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const kpi_plan_actual_controller_1 = require("./kpi-plan-actual.controller");
const kpi_plan_actual_service_1 = require("./kpi-plan-actual.service");
const jvs_kpi_plan_actual_entity_1 = require("./jvs-kpi-plan-actual.entity");
const jvs_kpi_perticular_entity_1 = require("../kpi-perticular/jvs-kpi-perticular.entity");
let KpiPlanActualModule = class KpiPlanActualModule {
};
exports.KpiPlanActualModule = KpiPlanActualModule;
exports.KpiPlanActualModule = KpiPlanActualModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_kpi_plan_actual_entity_1.JvsKpiPlanActual, jvs_kpi_perticular_entity_1.JvsKpiPerticular])],
        controllers: [kpi_plan_actual_controller_1.KpiPlanActualController],
        providers: [kpi_plan_actual_service_1.KpiPlanActualService],
        exports: [kpi_plan_actual_service_1.KpiPlanActualService],
    })
], KpiPlanActualModule);
//# sourceMappingURL=kpi-plan-actual.module.js.map