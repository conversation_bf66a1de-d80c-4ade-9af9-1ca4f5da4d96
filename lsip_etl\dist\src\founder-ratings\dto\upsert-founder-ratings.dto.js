"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertFounderRatingsDto = exports.FounderRatingRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class FounderRatingRecordDto {
    id;
    name;
    createddate;
    account__c;
    vision_strategic_thinking__c;
    adaptability_learnability__c;
    goal_orientation__c;
    org_building_ability_to_attract_high_q__c;
    narrative_building_story_telling__c;
    financial_prudence_burn_management__c;
    pace_of_execution_bias_to_action__c;
    ability_to_take_tough_decisions__c;
    leading_others_outcome_from_team__c;
    self_awareness__c;
    integrity_transparency__c;
    rating_date__c;
}
exports.FounderRatingRecordDto = FounderRatingRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the founder rating.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Rating name.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Timestamp when the rating was created.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "createddate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Associated account identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "account__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for vision and strategic thinking.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "vision_strategic_thinking__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for adaptability and learnability.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "adaptability_learnability__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for goal orientation.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "goal_orientation__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Score for org building and ability to attract high quality talent.',
        maxLength: 255,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "org_building_ability_to_attract_high_q__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for narrative building and storytelling.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "narrative_building_story_telling__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for financial prudence and burn management.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "financial_prudence_burn_management__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for pace of execution and bias to action.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "pace_of_execution_bias_to_action__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for ability to take tough decisions.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "ability_to_take_tough_decisions__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for leading others and team outcomes.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "leading_others_outcome_from_team__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for self awareness.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "self_awareness__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for integrity and transparency.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "integrity_transparency__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Timestamp of the rating entry.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], FounderRatingRecordDto.prototype, "rating_date__c", void 0);
class UpsertFounderRatingsDto {
    FounderRatings;
}
exports.UpsertFounderRatingsDto = UpsertFounderRatingsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [FounderRatingRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => FounderRatingRecordDto),
    __metadata("design:type", Array)
], UpsertFounderRatingsDto.prototype, "FounderRatings", void 0);
//# sourceMappingURL=upsert-founder-ratings.dto.js.map