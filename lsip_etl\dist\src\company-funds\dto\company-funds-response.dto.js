"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyFundsApiResponseDto = exports.CompanyFundsProcessedDataDto = exports.UnprocessedCompanyFundRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_company_funds_dto_1 = require("./upsert-company-funds.dto");
class UnprocessedCompanyFundRecordDto extends upsert_company_funds_dto_1.CompanyFundRecordDto {
    message;
    accountAttempted;
}
exports.UnprocessedCompanyFundRecordDto = UnprocessedCompanyFundRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedCompanyFundRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedCompanyFundRecordDto.prototype, "accountAttempted", void 0);
class CompanyFundsProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.CompanyFundsProcessedDataDto = CompanyFundsProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], CompanyFundsProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records that were persisted with warnings (e.g., unresolved account references).',
        type: () => [UnprocessedCompanyFundRecordDto],
    }),
    __metadata("design:type", Array)
], CompanyFundsProcessedDataDto.prototype, "unprocessedRecords", void 0);
class CompanyFundsApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.CompanyFundsApiResponseDto = CompanyFundsApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: CompanyFundsProcessedDataDto }),
    __metadata("design:type", CompanyFundsProcessedDataDto)
], CompanyFundsApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=company-funds-response.dto.js.map