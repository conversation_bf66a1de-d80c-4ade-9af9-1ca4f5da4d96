"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiMasterApiResponseDto = exports.KpiMasterProcessedDataDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
class KpiMasterProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.KpiMasterProcessedDataDto = KpiMasterProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], KpiMasterProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records that could not be processed (always empty for KPI Master).',
        type: () => [Object],
    }),
    __metadata("design:type", Array)
], KpiMasterProcessedDataDto.prototype, "unprocessedRecords", void 0);
class KpiMasterApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.KpiMasterApiResponseDto = KpiMasterApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: KpiMasterProcessedDataDto }),
    __metadata("design:type", KpiMasterProcessedDataDto)
], KpiMasterApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=kpi-master-response.dto.js.map