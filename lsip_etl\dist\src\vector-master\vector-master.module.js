"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorMasterModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const vector_master_controller_1 = require("./vector-master.controller");
const vector_master_service_1 = require("./vector-master.service");
const jvs_vector_master_entity_1 = require("./jvs-vector-master.entity");
let VectorMasterModule = class VectorMasterModule {
};
exports.VectorMasterModule = VectorMasterModule;
exports.VectorMasterModule = VectorMasterModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_vector_master_entity_1.JvsVectorMaster])],
        controllers: [vector_master_controller_1.VectorMasterController],
        providers: [vector_master_service_1.VectorMasterService],
        exports: [vector_master_service_1.VectorMasterService],
    })
], VectorMasterModule);
//# sourceMappingURL=vector-master.module.js.map