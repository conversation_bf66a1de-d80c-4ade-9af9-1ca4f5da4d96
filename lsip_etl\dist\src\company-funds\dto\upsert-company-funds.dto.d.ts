export declare class CompanyFundRecordDto {
    id: string;
    isdeleted?: boolean;
    name?: string;
    currencyisocode?: string;
    createddate?: string;
    createdbyid?: string;
    lastmodifieddate?: string;
    lastmodifiedbyid?: string;
    systemmodstamp?: string;
    lastactivitydate?: string;
    fund__c?: string;
    account__c?: string;
    board_member__c?: string;
    going_in_cost__c?: number;
    going_in_post_money_value_m__c?: number;
    going_in_pre_money_value_m__c?: number;
    initial_date_of_investment__c?: string;
    initial_investment_stage__c?: string;
    initial_ownership__c?: number;
    initial_revenue_stage__c?: string;
    initial_round__c?: string;
    lead_primary__c?: string;
    lead_secondary__c?: string;
    short_name__c?: string;
    key__c?: string;
}
export declare class UpsertCompanyFundsDto {
    CompanyFunds: CompanyFundRecordDto[];
}
export type CompanyFundRecord = CompanyFundRecordDto;
