"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validate = validate;
const REQUIRED_ENV_VARS = [
    'DB_HOST',
    'DB_PORT',
    'DB_USER',
    'DB_PASS',
    'DB_NAME',
    'DB_SCHEMA'
];
function validate(config) {
    const missing = REQUIRED_ENV_VARS.filter((key) => {
        const value = config[key];
        return value === undefined || value === null || value === '';
    });
    if (missing.length > 0) {
        throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
    const dbPort = Number(config.DB_PORT);
    if (Number.isNaN(dbPort) || dbPort <= 0) {
        throw new Error('DB_PORT must be a positive number');
    }
    const appPort = config.PORT;
    if (appPort !== undefined && appPort !== null && appPort !== '') {
        const parsedPort = Number(appPort);
        if (Number.isNaN(parsedPort) || parsedPort <= 0) {
            throw new Error('PORT must be a positive number');
        }
    }
    const nodeEnv = config.NODE_ENV;
    if (nodeEnv !== undefined && nodeEnv !== null && nodeEnv !== '') {
        const allowedValues = ['development', 'production', 'test'];
        if (typeof nodeEnv !== 'string' || !allowedValues.includes(nodeEnv)) {
            throw new Error(`NODE_ENV must be one of: ${allowedValues.join(', ')}`);
        }
    }
    return config;
}
//# sourceMappingURL=env.validation.js.map