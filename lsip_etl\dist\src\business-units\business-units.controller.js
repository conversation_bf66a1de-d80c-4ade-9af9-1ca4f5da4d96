"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessUnitsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const business_units_service_1 = require("./business-units.service");
const upsert_business_units_dto_1 = require("./dto/upsert-business-units.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const business_units_response_dto_1 = require("./dto/business-units-response.dto");
let BusinessUnitsController = class BusinessUnitsController {
    businessUnitsService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(businessUnitsService) {
        this.businessUnitsService = businessUnitsService;
    }
    async upsertBusinessUnits(payload) {
        const result = await this.businessUnitsService.upsertBusinessUnits(payload.BusinessUnits);
        return (0, api_response_dto_1.createSuccessResponse)('Business units processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.BusinessUnitsController = BusinessUnitsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(BusinessUnitsController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert business unit data.' }),
    (0, swagger_1.ApiBody)({ type: upsert_business_units_dto_1.UpsertBusinessUnitsDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Business units processed successfully.',
        type: business_units_response_dto_1.BusinessUnitsApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_business_units_dto_1.UpsertBusinessUnitsDto]),
    __metadata("design:returntype", Promise)
], BusinessUnitsController.prototype, "upsertBusinessUnits", null);
exports.BusinessUnitsController = BusinessUnitsController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('business-units'),
    __metadata("design:paramtypes", [business_units_service_1.BusinessUnitsService])
], BusinessUnitsController);
//# sourceMappingURL=business-units.controller.js.map