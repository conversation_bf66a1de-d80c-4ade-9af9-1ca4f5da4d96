"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiPlanActualApiResponseDto = exports.KpiPlanActualProcessedDataDto = exports.UnprocessedKpiPlanActualRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_kpi_plan_actual_dto_1 = require("./upsert-kpi-plan-actual.dto");
class UnprocessedKpiPlanActualRecordDto extends upsert_kpi_plan_actual_dto_1.KpiPlanActualRecordDto {
    message;
    kpiPerticularAttempted;
}
exports.UnprocessedKpiPlanActualRecordDto = UnprocessedKpiPlanActualRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedKpiPlanActualRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KPI particular identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedKpiPlanActualRecordDto.prototype, "kpiPerticularAttempted", void 0);
class KpiPlanActualProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.KpiPlanActualProcessedDataDto = KpiPlanActualProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], KpiPlanActualProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records persisted with warnings.',
        type: () => [UnprocessedKpiPlanActualRecordDto],
    }),
    __metadata("design:type", Array)
], KpiPlanActualProcessedDataDto.prototype, "unprocessedRecords", void 0);
class KpiPlanActualApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.KpiPlanActualApiResponseDto = KpiPlanActualApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: KpiPlanActualProcessedDataDto }),
    __metadata("design:type", KpiPlanActualProcessedDataDto)
], KpiPlanActualApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=kpi-plan-actual-response.dto.js.map