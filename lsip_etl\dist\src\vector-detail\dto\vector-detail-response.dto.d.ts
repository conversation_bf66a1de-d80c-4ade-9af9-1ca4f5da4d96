import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { VectorDetailRecordDto } from './upsert-vector-detail.dto';
export declare class UnprocessedVectorDetailRecordDto extends VectorDetailRecordDto {
    message: string;
    kpiPerticularAttempted: string | null;
    vectorMasterAttempted: string | null;
}
export declare class VectorDetailProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedVectorDetailRecordDto[];
}
export declare class VectorDetailApiResponseDto extends BaseApiResponseDto {
    data: VectorDetailProcessedDataDto;
}
