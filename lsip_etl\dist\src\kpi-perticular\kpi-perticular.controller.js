"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiPerticularController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const kpi_perticular_service_1 = require("./kpi-perticular.service");
const upsert_kpi_perticular_dto_1 = require("./dto/upsert-kpi-perticular.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const kpi_perticular_response_dto_1 = require("./dto/kpi-perticular-response.dto");
let KpiPerticularController = class KpiPerticularController {
    kpiPerticularService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(kpiPerticularService) {
        this.kpiPerticularService = kpiPerticularService;
    }
    async upsertKpiPerticulars(payload) {
        const result = await this.kpiPerticularService.upsertKpiPerticulars(payload.KpiPerticulars);
        return (0, api_response_dto_1.createSuccessResponse)('KPI particular records processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.KpiPerticularController = KpiPerticularController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(KpiPerticularController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert KPI particular records.' }),
    (0, swagger_1.ApiBody)({ type: upsert_kpi_perticular_dto_1.UpsertKpiPerticularDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'KPI particular records processed successfully.',
        type: kpi_perticular_response_dto_1.KpiPerticularApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_kpi_perticular_dto_1.UpsertKpiPerticularDto]),
    __metadata("design:returntype", Promise)
], KpiPerticularController.prototype, "upsertKpiPerticulars", null);
exports.KpiPerticularController = KpiPerticularController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('kpi-perticulars'),
    __metadata("design:paramtypes", [kpi_perticular_service_1.KpiPerticularService])
], KpiPerticularController);
//# sourceMappingURL=kpi-perticular.controller.js.map