"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const contacts_service_1 = require("./contacts.service");
const upsert_contacts_dto_1 = require("./dto/upsert-contacts.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const contacts_response_dto_1 = require("./dto/contacts-response.dto");
let ContactsController = class ContactsController {
    contactsService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(contactsService) {
        this.contactsService = contactsService;
    }
    async upsertContacts(payload) {
        const result = await this.contactsService.upsertContacts(payload.Contacts);
        return (0, api_response_dto_1.createSuccessResponse)('Contacts processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.ContactsController = ContactsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(ContactsController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert contact data.' }),
    (0, swagger_1.ApiBody)({ type: upsert_contacts_dto_1.UpsertContactsDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Contacts processed successfully.',
        type: contacts_response_dto_1.ContactsApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_contacts_dto_1.UpsertContactsDto]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "upsertContacts", null);
exports.ContactsController = ContactsController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('contacts'),
    __metadata("design:paramtypes", [contacts_service_1.ContactsService])
], ContactsController);
//# sourceMappingURL=contacts.controller.js.map