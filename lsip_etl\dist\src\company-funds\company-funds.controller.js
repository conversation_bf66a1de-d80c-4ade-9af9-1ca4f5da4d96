"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyFundsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const company_funds_service_1 = require("./company-funds.service");
const upsert_company_funds_dto_1 = require("./dto/upsert-company-funds.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const company_funds_response_dto_1 = require("./dto/company-funds-response.dto");
let CompanyFundsController = class CompanyFundsController {
    companyFundsService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(companyFundsService) {
        this.companyFundsService = companyFundsService;
    }
    async upsertCompanyFunds(payload) {
        const result = await this.companyFundsService.upsertCompanyFunds(payload.CompanyFunds);
        return (0, api_response_dto_1.createSuccessResponse)('Company funds processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.CompanyFundsController = CompanyFundsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(CompanyFundsController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert company fund data.' }),
    (0, swagger_1.ApiBody)({ type: upsert_company_funds_dto_1.UpsertCompanyFundsDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Company funds processed successfully.',
        type: company_funds_response_dto_1.CompanyFundsApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_company_funds_dto_1.UpsertCompanyFundsDto]),
    __metadata("design:returntype", Promise)
], CompanyFundsController.prototype, "upsertCompanyFunds", null);
exports.CompanyFundsController = CompanyFundsController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('company-funds'),
    __metadata("design:paramtypes", [company_funds_service_1.CompanyFundsService])
], CompanyFundsController);
//# sourceMappingURL=company-funds.controller.js.map