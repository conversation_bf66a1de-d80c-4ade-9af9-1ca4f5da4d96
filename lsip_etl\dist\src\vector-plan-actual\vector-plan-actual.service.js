"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorPlanActualService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_vector_plan_actual_entity_1 = require("./jvs-vector-plan-actual.entity");
const jvs_vector_detail_entity_1 = require("../vector-detail/jvs-vector-detail.entity");
const jvs_kpi_plan_actual_entity_1 = require("../kpi-plan-actual/jvs-kpi-plan-actual.entity");
let VectorPlanActualService = class VectorPlanActualService {
    vectorPlanActualRepository;
    vectorDetailRepository;
    kpiPlanActualRepository;
    constructor(vectorPlanActualRepository, vectorDetailRepository, kpiPlanActualRepository) {
        this.vectorPlanActualRepository = vectorPlanActualRepository;
        this.vectorDetailRepository = vectorDetailRepository;
        this.kpiPlanActualRepository = kpiPlanActualRepository;
    }
    async upsertVectorPlanActuals(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedVectorDetailIds = new Set(records.map((record) => record.vectorDetailId).filter((value) => Boolean(value)));
        const requestedKpiPlanActualIds = new Set(records.map((record) => record.kpiPlanActualId).filter((value) => Boolean(value)));
        const existingVectorDetailIds = requestedVectorDetailIds.size
            ? new Set((await this.vectorDetailRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedVectorDetailIds]) },
            })).map((detail) => detail.id))
            : new Set();
        const existingKpiPlanActualIds = requestedKpiPlanActualIds.size
            ? new Set((await this.kpiPlanActualRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)([...requestedKpiPlanActualIds]) },
            })).map((planActual) => planActual.id))
            : new Set();
        const entities = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                vectorDetailAttempted: null,
                kpiPlanActualAttempted: null,
            };
            const vectorDetailId = record.vectorDetailId && existingVectorDetailIds.has(record.vectorDetailId)
                ? record.vectorDetailId
                : null;
            if (record.vectorDetailId && !vectorDetailId) {
                meta.vectorDetailAttempted = record.vectorDetailId;
                meta.issues.push(`Vector detail ${record.vectorDetailId} not found; stored without vector detail linkage.`);
            }
            const kpiPlanActualId = record.kpiPlanActualId && existingKpiPlanActualIds.has(record.kpiPlanActualId)
                ? record.kpiPlanActualId
                : null;
            if (record.kpiPlanActualId && !kpiPlanActualId) {
                meta.kpiPlanActualAttempted = record.kpiPlanActualId;
                meta.issues.push(`KPI plan actual ${record.kpiPlanActualId} not found; stored without KPI plan actual linkage.`);
            }
            const entity = this.vectorPlanActualRepository.create({
                id: record.id,
                name: record.name ?? null,
                currencyIsoCode: record.currencyIsoCode ?? null,
                actual: record.actual ?? null,
                month1: record.month1 ?? null,
                alternateName: record.alternateName ?? null,
                numberOfPlanRevisions: record.numberOfPlanRevisions ?? null,
                plan: record.plan ?? null,
                quarter: record.quarter ?? null,
                reasonForDeviationRemarks: record.reasonForDeviationRemarks ?? null,
                uniqueIdentifier: record.uniqueIdentifier ?? null,
                vectorDetailId,
                year: record.year ?? null,
                monthNumber: record.monthNumber ?? null,
                consolidatedDate: record.consolidatedDate ?? null,
                kpiPlanActualId,
                planActualDate: record.planActualDate ? new Date(record.planActualDate) : null,
            });
            metaById.set(record.id, meta);
            entities.push(entity);
        }
        if (entities.length > 0) {
            await this.vectorPlanActualRepository.save(entities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    vectorDetailAttempted: meta.vectorDetailAttempted,
                    kpiPlanActualAttempted: meta.kpiPlanActualAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.VectorPlanActualService = VectorPlanActualService;
exports.VectorPlanActualService = VectorPlanActualService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_vector_plan_actual_entity_1.JvsVectorPlanActual)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_vector_detail_entity_1.JvsVectorDetail)),
    __param(2, (0, typeorm_1.InjectRepository)(jvs_kpi_plan_actual_entity_1.JvsKpiPlanActual)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], VectorPlanActualService);
//# sourceMappingURL=vector-plan-actual.service.js.map