"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertKpiPerticularDto = exports.KpiPerticularRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class KpiPerticularRecordDto {
    id;
    name;
    currencyisocode;
    business_unit__c;
    company__c;
    creation_type__c;
    formulaid__c;
    formula__c;
    kpi_master__c;
    kpi_particular_level_number__c;
    kpi_particular_level__c;
    name_set__c;
    name__c;
    plan_required__c;
    sub_business_unit__c;
    unique_identifier__c;
    benchmark__c;
    industry__c;
    kpi_particular_type__c;
    sub_industry__c;
    entity_id__c;
}
exports.KpiPerticularRecordDto = KpiPerticularRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the KPI particular record.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Display name.', maxLength: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(80),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Currency ISO code.', maxLength: 3 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 3),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "currencyisocode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Business unit identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "business_unit__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Account identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "company__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Creation type.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "creation_type__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Salesforce formula ID.', maxLength: 32768 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(32768),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "formulaid__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Formula definition.', maxLength: 131072 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(131072),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "formula__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'KPI master identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "kpi_master__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Numeric representation of the KPI level.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], KpiPerticularRecordDto.prototype, "kpi_particular_level_number__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Textual representation of the KPI level.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "kpi_particular_level__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Flag indicating if the name was set manually.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], KpiPerticularRecordDto.prototype, "name_set__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Alternate name.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "name__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Indicates if planning is required.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], KpiPerticularRecordDto.prototype, "plan_required__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Sub business unit identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "sub_business_unit__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Unique identifier string.', maxLength: 225 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(225),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "unique_identifier__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Benchmark identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "benchmark__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Industry identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "industry__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'KPI particular type.', maxLength: 255 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "kpi_particular_type__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Sub-industry identifier.', maxLength: 18 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "sub_industry__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Entity identifier list.', maxLength: 1300 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1300),
    __metadata("design:type", String)
], KpiPerticularRecordDto.prototype, "entity_id__c", void 0);
class UpsertKpiPerticularDto {
    KpiPerticulars;
}
exports.UpsertKpiPerticularDto = UpsertKpiPerticularDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [KpiPerticularRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => KpiPerticularRecordDto),
    __metadata("design:type", Array)
], UpsertKpiPerticularDto.prototype, "KpiPerticulars", void 0);
//# sourceMappingURL=upsert-kpi-perticular.dto.js.map