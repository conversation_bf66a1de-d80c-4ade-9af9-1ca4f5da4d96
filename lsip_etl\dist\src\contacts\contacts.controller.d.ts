import { ContactsService } from './contacts.service';
import { UpsertContactsDto } from './dto/upsert-contacts.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class ContactsController {
    private readonly contactsService;
    private static readonly validationPipe;
    constructor(contactsService: ContactsService);
    upsertContacts(payload: UpsertContactsDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            accountAttempted: string | null;
            companyFundAttempted: string | null;
            reportsToAttempted: string | null;
        }[];
    }>>;
}
