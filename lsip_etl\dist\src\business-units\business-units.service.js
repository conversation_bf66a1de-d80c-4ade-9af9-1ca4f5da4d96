"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessUnitsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_business_unit_entity_1 = require("./jvs-business-unit.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
let BusinessUnitsService = class BusinessUnitsService {
    businessUnitRepository;
    accountRepository;
    constructor(businessUnitRepository, accountRepository) {
        this.businessUnitRepository = businessUnitRepository;
        this.accountRepository = accountRepository;
    }
    async upsertBusinessUnits(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedCompanyIds = Array.from(new Set(records.map((record) => record.company__c).filter((value) => Boolean(value))));
        const existingCompanyIds = requestedCompanyIds.length
            ? new Set((await this.accountRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedCompanyIds) },
            })).map((account) => account.id))
            : new Set();
        const topLevelUnits = [];
        const childCandidates = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                companyAttempted: null,
                parentBusinessAttempted: null,
            };
            const companyId = record.company__c && existingCompanyIds.has(record.company__c) ? record.company__c : null;
            if (record.company__c && !companyId) {
                meta.companyAttempted = record.company__c;
                meta.issues.push(`Account ${record.company__c} not found; stored without company linkage.`);
            }
            const entity = this.businessUnitRepository.create({
                id: record.id,
                name: record.name ?? null,
                companyId,
                parentBusinessId: null,
                active: record.active__c ?? null,
                benchmarkId: record.benchmark__c ?? null,
                type: record.type__c ?? null,
            });
            metaById.set(record.id, meta);
            if (record.parent_business__c) {
                childCandidates.push({ entity, parentId: record.parent_business__c });
            }
            else {
                topLevelUnits.push(entity);
            }
        }
        if (topLevelUnits.length > 0) {
            await this.businessUnitRepository.save(topLevelUnits);
        }
        const requestedParentIds = Array.from(new Set(childCandidates.map((candidate) => candidate.parentId)));
        const existingParents = requestedParentIds.length
            ? await this.businessUnitRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedParentIds) },
            })
            : [];
        const resolvedBusinessUnitIds = new Set([
            ...existingParents.map((unit) => unit.id),
            ...topLevelUnits.map((unit) => unit.id),
        ]);
        const childEntities = [];
        let pendingCandidates = [...childCandidates];
        while (pendingCandidates.length > 0) {
            const deferredCandidates = [];
            let processedInCurrentPass = false;
            for (const candidate of pendingCandidates) {
                if (resolvedBusinessUnitIds.has(candidate.parentId)) {
                    candidate.entity.parentBusinessId = candidate.parentId;
                    resolvedBusinessUnitIds.add(candidate.entity.id);
                    childEntities.push(candidate.entity);
                    processedInCurrentPass = true;
                }
                else {
                    deferredCandidates.push(candidate);
                }
            }
            if (!processedInCurrentPass) {
                for (const candidate of deferredCandidates) {
                    candidate.entity.parentBusinessId = null;
                    childEntities.push(candidate.entity);
                    const meta = metaById.get(candidate.entity.id);
                    if (meta) {
                        meta.parentBusinessAttempted = candidate.parentId;
                        meta.issues.push(`Parent business unit ${candidate.parentId} not found; stored without parent linkage.`);
                    }
                }
                break;
            }
            pendingCandidates = deferredCandidates;
        }
        if (childEntities.length > 0) {
            await this.businessUnitRepository.save(childEntities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    companyAttempted: meta.companyAttempted,
                    parentBusinessAttempted: meta.parentBusinessAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.BusinessUnitsService = BusinessUnitsService;
exports.BusinessUnitsService = BusinessUnitsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_business_unit_entity_1.JvsBusinessUnit)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], BusinessUnitsService);
//# sourceMappingURL=business-units.service.js.map