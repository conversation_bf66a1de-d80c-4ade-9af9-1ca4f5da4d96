"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const googleapis_1 = require("googleapis");
const xlsx_1 = __importDefault(require("xlsx"));
const KEYFILEPATH = "C:\Users\<USER>\Desktop\project\lsip_etl\internship-poc-473709-2650dce5e12a.json";
const SCOPES = ["https://www.googleapis.com/auth/drive.readonly"];
const auth = new googleapis_1.google.auth.GoogleAuth({
    keyFile: KEYFILEPATH,
    scopes: SCOPES,
});
const drive = googleapis_1.google.drive({ version: "v3", auth });
const FILE_ID = "1krtI31BYIf3f5Ei2ATlInsgZjuQIAUiC";
async function downloadExcel() {
    const response = await drive.files.get({ fileId: FILE_ID, alt: "media" }, { responseType: "arraybuffer" });
    const buffer = Buffer.from(response.data);
    const workbook = xlsx_1.default.read(buffer, { type: "buffer" });
    console.log('Size of Sheets : ', workbook.SheetNames);
    for (let i = 0; i < workbook.SheetNames.length; i++) {
        if (workbook.SheetNames[i] === "Cap table") {
            const sheetName = workbook.SheetNames[i];
            const sheet = workbook.Sheets[sheetName];
            const jsonData = xlsx_1.default.utils.sheet_to_json(sheet);
            const jsonString = JSON.stringify(jsonData, null, 2);
            const fileName = workbook.SheetNames[i].trim();
            fs_1.default.writeFileSync(`src/ExcelJsonData/${fileName}.json`, jsonString, "utf-8");
        }
    }
    console.log("JSON data has been saved");
}
downloadExcel();
//# sourceMappingURL=excelController.js.map