"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndustriesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const industries_service_1 = require("./industries.service");
const upsert_industries_dto_1 = require("./dto/upsert-industries.dto");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const industries_response_dto_1 = require("./dto/industries-response.dto");
let IndustriesController = class IndustriesController {
    industriesService;
    static validationPipe = new common_1.ValidationPipe({ whitelist: true, transform: true });
    constructor(industriesService) {
        this.industriesService = industriesService;
    }
    async upsertIndustries(payload) {
        const result = await this.industriesService.upsertIndustries(payload.Industries);
        return (0, api_response_dto_1.createSuccessResponse)('Industries processed successfully.', {
            processed: result.processed,
            unprocessedRecords: result.unprocessedRecords,
        });
    }
};
exports.IndustriesController = IndustriesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UsePipes)(IndustriesController.validationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Upsert industries data.' }),
    (0, swagger_1.ApiBody)({ type: upsert_industries_dto_1.UpsertIndustriesDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Industries processed successfully.',
        type: industries_response_dto_1.IndustriesApiResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upsert_industries_dto_1.UpsertIndustriesDto]),
    __metadata("design:returntype", Promise)
], IndustriesController.prototype, "upsertIndustries", null);
exports.IndustriesController = IndustriesController = __decorate([
    (0, swagger_1.ApiTags)('ETL Api'),
    (0, common_1.Controller)('industries'),
    __metadata("design:paramtypes", [industries_service_1.IndustriesService])
], IndustriesController);
//# sourceMappingURL=industries.controller.js.map