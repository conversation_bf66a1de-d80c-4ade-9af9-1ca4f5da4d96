import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { BusinessRatingRecordDto } from './upsert-business-ratings.dto';
export declare class UnprocessedBusinessRatingRecordDto extends BusinessRatingRecordDto {
    message: string;
    accountAttempted: string | null;
}
export declare class BusinessRatingsProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedBusinessRatingRecordDto[];
}
export declare class BusinessRatingsApiResponseDto extends BaseApiResponseDto {
    data: BusinessRatingsProcessedDataDto;
}
