"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJvsCompanyFundTable************* = void 0;
class CreateJvsCompanyFundTable************* {
    name = 'CreateJvsCompanyFundTable*************';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_company_fund (
              id character varying(18) NOT NULL,
              isdeleted boolean,
              name character varying(80),
              currencyisocode character varying(3),
              createddate text,
              createdbyid character varying(18),
              lastmodifieddate timestamp with time zone,
              lastmodifiedbyid character varying(18),
              systemmodstamp text,
              lastactivitydate date,
              fund__c character varying(18),
              account__c character varying(18),
              board_member__c character varying(18),
              going_in_cost__c double precision,
              going_in_post_money_value_m__c double precision,
              going_in_pre_money_value_m__c double precision,
              initial_date_of_investment__c date,
              initial_investment_stage__c character varying(255),
              initial_ownership__c double precision,
              initial_revenue_stage__c character varying(255),
              initial_round__c character varying(255),
              lead_primary__c character varying(18),
              lead_secondary__c character varying(18),
              short_name__c character varying(50),
              key__c character varying(100),
              CONSTRAINT pk_jvs_company_fund_id PRIMARY KEY (id)
            );
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_company_fund_account__c ON jvs_company_fund (account__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_company_fund_fund__c ON jvs_company_fund (fund__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_company_fund_board_member__c ON jvs_company_fund (board_member__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_company_fund_lead_primary__c ON jvs_company_fund (lead_primary__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_company_fund_lead_secondary__c ON jvs_company_fund (lead_secondary__c);
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_company_fund ADD CONSTRAINT fk_jvs_company_fund_account__c FOREIGN KEY (account__c) REFERENCES jvs_accounts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE jvs_company_fund DROP CONSTRAINT IF EXISTS fk_jvs_company_fund_account__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_company_fund_lead_secondary__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_company_fund_lead_primary__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_company_fund_board_member__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_company_fund_fund__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_company_fund_account__c;
    `);
        await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_company_fund;
    `);
    }
}
exports.CreateJvsCompanyFundTable************* = CreateJvsCompanyFundTable*************;
//# sourceMappingURL=*************-CreateJvsCompanyFundTable.js.map