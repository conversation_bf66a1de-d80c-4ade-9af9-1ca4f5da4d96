import * as fs from 'fs';
import * as path from 'path';
import * as http from 'http';
import * as https from 'https';

// Configuration
const SERVER_URL = 'http://localhost:3000'; // Change this if your server runs on a different port
const ENDPOINT = '/company-funds';
const JSON_FILE_PATH = path.join(__dirname, 'src', 'ExcelJsonData', 'Cap table.json');

interface CompanyFundRecord {
  id: string;
  isdeleted?: boolean;
  name?: string;
  currencyisocode?: string;
  createddate?: string;
  createdbyid?: string;
  lastmodifieddate?: string;
  lastmodifiedbyid?: string;
  systemmodstamp?: string;
  lastactivitydate?: string;
  fund__c?: string;
  account__c?: string;
  board_member__c?: string;
  going_in_cost__c?: number;
  going_in_post_money_value_m__c?: number;
  going_in_pre_money_value_m__c?: number;
  initial_date_of_investment__c?: string;
  initial_investment_stage__c?: string;
  initial_ownership__c?: number;
  initial_revenue_stage__c?: string;
  initial_round__c?: string;
  lead_primary__c?: string;
  lead_secondary__c?: string;
  short_name__c?: string;
  key__c?: string;
}

interface CompanyFundsPayload {
  CompanyFunds: CompanyFundRecord[];
}

interface HttpResponse {
  statusCode: number;
  headers: http.IncomingHttpHeaders;
  body: any;
}

async function sendCapTableData(): Promise<void> {
  try {
    // Read the JSON file
    console.log('Reading JSON file:', JSON_FILE_PATH);
    
    if (!fs.existsSync(JSON_FILE_PATH)) {
      throw new Error(`JSON file not found at: ${JSON_FILE_PATH}`);
    }
    
    const jsonData = fs.readFileSync(JSON_FILE_PATH, 'utf8');
    const payload: CompanyFundsPayload = JSON.parse(jsonData);
    
    console.log('Loaded data:', JSON.stringify(payload, null, 2));
    console.log(`Found ${payload.CompanyFunds?.length || 0} company fund records`);
    
    // Prepare the HTTP request
    const url = new URL(SERVER_URL + ENDPOINT);
    const postData = JSON.stringify(payload);
    
    const options: http.RequestOptions = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    console.log(`\nSending POST request to: ${SERVER_URL}${ENDPOINT}`);
    console.log('Request options:', options);
    
    // Send the request
    const response = await makeRequest(options, postData);
    
    console.log('\n✅ Success! Response received:');
    console.log('Status:', response.statusCode);
    console.log('Headers:', response.headers);
    console.log('Body:', response.body);
    
  } catch (error) {
    console.error('❌ Error:', (error as Error).message);
    if ((error as any).code === 'ECONNREFUSED') {
      console.error('\n💡 Make sure your NestJS server is running on', SERVER_URL);
      console.error('   You can start it with: npm run start:dev');
    }
    process.exit(1);
  }
}

function makeRequest(options: http.RequestOptions, postData: string): Promise<HttpResponse> {
  return new Promise((resolve, reject) => {
    const protocol = options.port === 443 ? https : http;
    
    const req = protocol.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode!,
            headers: res.headers,
            body: parsedBody
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode!,
            headers: res.headers,
            body: body
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.write(postData);
    req.end();
  });
}

// Run the script
if (require.main === module) {
  sendCapTableData();
}

export { sendCapTableData };
