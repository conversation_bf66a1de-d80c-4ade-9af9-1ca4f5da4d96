"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorMasterApiResponseDto = exports.VectorMasterProcessedDataDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
class VectorMasterProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.VectorMasterProcessedDataDto = VectorMasterProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], VectorMasterProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Vector master has no relationship warnings; list remains empty.',
        type: () => [Object],
    }),
    __metadata("design:type", Array)
], VectorMasterProcessedDataDto.prototype, "unprocessedRecords", void 0);
class VectorMasterApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.VectorMasterApiResponseDto = VectorMasterApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: VectorMasterProcessedDataDto }),
    __metadata("design:type", VectorMasterProcessedDataDto)
], VectorMasterApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=vector-master-response.dto.js.map