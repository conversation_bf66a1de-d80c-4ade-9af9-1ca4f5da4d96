import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { AccountRecordDto } from './upsert-accounts.dto';
export declare class UnprocessedAccountRecordDto extends AccountRecordDto {
    message: string;
    parentAccountAttempted: string | null;
    industryMasterAttempted: string | null;
    subIndustryAttempted: string | null;
}
export declare class AccountsProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedAccountRecordDto[];
}
export declare class AccountsApiResponseDto extends BaseApiResponseDto {
    data: AccountsProcessedDataDto;
}
