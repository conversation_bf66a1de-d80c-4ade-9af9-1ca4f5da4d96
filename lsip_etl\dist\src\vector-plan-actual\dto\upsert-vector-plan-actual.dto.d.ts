export declare class VectorPlanActualRecordDto {
    id: string;
    name?: string;
    currencyIsoCode?: string;
    actual?: string;
    month1?: string;
    alternateName?: string;
    numberOfPlanRevisions?: number;
    plan?: string;
    quarter?: string;
    reasonForDeviationRemarks?: string;
    uniqueIdentifier?: string;
    vectorDetailId?: string;
    year?: string;
    monthNumber?: number;
    consolidatedDate?: string;
    kpiPlanActualId?: string;
    planActualDate?: string;
}
export declare class UpsertVectorPlanActualDto {
    VectorPlanActuals: VectorPlanActualRecordDto[];
}
export type VectorPlanActualRecord = VectorPlanActualRecordDto;
