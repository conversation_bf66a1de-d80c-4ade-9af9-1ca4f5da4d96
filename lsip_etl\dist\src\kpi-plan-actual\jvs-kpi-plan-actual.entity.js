"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsKpiPlanActual = void 0;
const typeorm_1 = require("typeorm");
let JvsKpiPlanActual = class JvsKpiPlanActual {
    id;
    name = null;
    actual = null;
    kpiPerticularId = null;
    month1 = null;
    alternateName = null;
    numberOfPlanRevisions = null;
    plan = null;
    quarter = null;
    reasonForDeviationRemarks = null;
    year = null;
    actualFormula = null;
    monthNumber = null;
    percentageOfAchievement = null;
    consolidatedDate = null;
    planActualDate = null;
};
exports.JvsKpiPlanActual = JvsKpiPlanActual;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsKpiPlanActual.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'actual__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "actual", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kpi_particular__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "kpiPerticularId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'month1__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "month1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'name__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "alternateName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_of_plan_revisions__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "numberOfPlanRevisions", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'plan__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "plan", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'quarter__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "quarter", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reason_for_deviation_remarks__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "reasonForDeviationRemarks", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'year__c', type: 'varchar', length: 4, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "year", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'actual_formula__c', type: 'varchar', length: 1300, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "actualFormula", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'month_number__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "monthNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'percentage_of_achievement__c', type: 'double precision', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "percentageOfAchievement", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'consolidated_date__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "consolidatedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'plan_actual_date__c', type: 'date', nullable: true }),
    __metadata("design:type", Object)
], JvsKpiPlanActual.prototype, "planActualDate", void 0);
exports.JvsKpiPlanActual = JvsKpiPlanActual = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_kpi_plan_actual' })
], JvsKpiPlanActual);
//# sourceMappingURL=jvs-kpi-plan-actual.entity.js.map