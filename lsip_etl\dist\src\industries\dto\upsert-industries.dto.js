"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertIndustriesDto = exports.IndustryRecordDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class IndustryRecordDto {
    id;
    name;
    frequency__c;
    parent_industry__c;
}
exports.IndustryRecordDto = IndustryRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier of the industry.', maxLength: 18 }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], IndustryRecordDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Industry or sub-industry name.', maxLength: 80, example: 'Technology' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 80),
    __metadata("design:type", String)
], IndustryRecordDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Reporting frequency.', maxLength: 255, example: 'Monthly' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 255),
    __metadata("design:type", String)
], IndustryRecordDto.prototype, "frequency__c", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Parent industry identifier; when provided, the record is treated as a sub-industry.',
        maxLength: 18,
        example: 'a0Xez0000000QfFEAU',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 18),
    __metadata("design:type", String)
], IndustryRecordDto.prototype, "parent_industry__c", void 0);
class UpsertIndustriesDto {
    Industries;
}
exports.UpsertIndustriesDto = UpsertIndustriesDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [IndustryRecordDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => IndustryRecordDto),
    __metadata("design:type", Array)
], UpsertIndustriesDto.prototype, "Industries", void 0);
//# sourceMappingURL=upsert-industries.dto.js.map