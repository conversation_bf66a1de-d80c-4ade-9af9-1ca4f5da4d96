export declare class ContactRecordDto {
    id: string;
    isdeleted?: boolean;
    masterrecordid?: string;
    accountid?: string;
    lastname?: string;
    firstname?: string;
    salutation?: string;
    middlename?: string;
    name?: string;
    phone?: string;
    fax?: string;
    mobilephone?: string;
    assistantphone?: string;
    reportstoid?: string;
    email?: string;
    title?: string;
    department?: string;
    assistantname?: string;
    birthdate?: string;
    hasoptedoutofemail?: boolean;
    hasoptedoutoffax?: boolean;
    donotcall?: boolean;
    titletype?: string;
    departmentgroup?: string;
    company_fund__c?: string;
    dayofmonth__c?: number;
    designation_rank__c?: number;
    dob__c?: string;
    director__c?: boolean;
    department_2__c?: string;
    designation__c?: string;
    key_contact__c?: boolean;
    rating__c?: string;
}
export declare class UpsertContactsDto {
    Contacts: ContactRecordDto[];
}
export type ContactRecord = ContactRecordDto;
