"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jvs_contact_entity_1 = require("./jvs-contact.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
const jvs_company_fund_entity_1 = require("../company-funds/jvs-company-fund.entity");
let ContactsService = class ContactsService {
    contactRepository;
    accountRepository;
    companyFundRepository;
    constructor(contactRepository, accountRepository, companyFundRepository) {
        this.contactRepository = contactRepository;
        this.accountRepository = accountRepository;
        this.companyFundRepository = companyFundRepository;
    }
    async upsertContacts(records) {
        if (records.length === 0) {
            return { processed: 0, unprocessedRecords: [] };
        }
        const requestedAccountIds = Array.from(new Set(records.map((record) => record.accountid).filter((value) => Boolean(value))));
        const requestedCompanyFundIds = Array.from(new Set(records.map((record) => record.company_fund__c).filter((value) => Boolean(value))));
        const existingAccountIds = requestedAccountIds.length
            ? new Set((await this.accountRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedAccountIds) },
            })).map((account) => account.id))
            : new Set();
        const existingCompanyFundIds = requestedCompanyFundIds.length
            ? new Set((await this.companyFundRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedCompanyFundIds) },
            })).map((companyFund) => companyFund.id))
            : new Set();
        const topLevelContacts = [];
        const childCandidates = [];
        const metaById = new Map();
        for (const record of records) {
            const meta = {
                original: { ...record },
                issues: [],
                accountAttempted: null,
                companyFundAttempted: null,
                reportsToAttempted: null,
            };
            const accountId = record.accountid && existingAccountIds.has(record.accountid) ? record.accountid : null;
            if (record.accountid && !accountId) {
                meta.accountAttempted = record.accountid;
                meta.issues.push(`Account ${record.accountid} not found; stored without account linkage.`);
            }
            const companyFundId = record.company_fund__c && existingCompanyFundIds.has(record.company_fund__c)
                ? record.company_fund__c
                : null;
            if (record.company_fund__c && !companyFundId) {
                meta.companyFundAttempted = record.company_fund__c;
                meta.issues.push(`Company fund ${record.company_fund__c} not found; stored without company fund linkage.`);
            }
            const entity = this.contactRepository.create({
                id: record.id,
                isDeleted: record.isdeleted ?? null,
                masterRecordId: record.masterrecordid ?? null,
                accountId,
                lastName: record.lastname ?? null,
                firstName: record.firstname ?? null,
                salutation: record.salutation ?? null,
                middleName: record.middlename ?? null,
                name: record.name ?? null,
                phone: record.phone ?? null,
                fax: record.fax ?? null,
                mobilePhone: record.mobilephone ?? null,
                assistantPhone: record.assistantphone ?? null,
                reportsToId: null,
                email: record.email ?? null,
                title: record.title ?? null,
                department: record.department ?? null,
                assistantName: record.assistantname ?? null,
                birthDate: record.birthdate ? new Date(record.birthdate) : null,
                hasOptedOutOfEmail: record.hasoptedoutofemail ?? null,
                hasOptedOutOfFax: record.hasoptedoutoffax ?? null,
                doNotCall: record.donotcall ?? null,
                titleType: record.titletype ?? null,
                departmentGroup: record.departmentgroup ?? null,
                companyFundId,
                dayOfMonth: record.dayofmonth__c ?? null,
                designationRank: record.designation_rank__c ?? null,
                dateOfBirth: record.dob__c ? new Date(record.dob__c) : null,
                director: record.director__c ?? null,
                department2: record.department_2__c ?? null,
                designation: record.designation__c ?? null,
                keyContact: record.key_contact__c ?? null,
                rating: record.rating__c ?? null,
            });
            metaById.set(record.id, meta);
            if (record.reportstoid) {
                childCandidates.push({ entity, parentId: record.reportstoid });
            }
            else {
                topLevelContacts.push(entity);
            }
        }
        if (topLevelContacts.length > 0) {
            await this.contactRepository.save(topLevelContacts);
        }
        const requestedReportsToIds = Array.from(new Set(childCandidates.map((candidate) => candidate.parentId)));
        const existingParents = requestedReportsToIds.length
            ? await this.contactRepository.find({
                select: ['id'],
                where: { id: (0, typeorm_2.In)(requestedReportsToIds) },
            })
            : [];
        const resolvedContactIds = new Set([
            ...existingParents.map((contact) => contact.id),
            ...topLevelContacts.map((contact) => contact.id),
        ]);
        const childEntities = [];
        let pendingCandidates = [...childCandidates];
        while (pendingCandidates.length > 0) {
            const deferredCandidates = [];
            let processedInCurrentPass = false;
            for (const candidate of pendingCandidates) {
                if (resolvedContactIds.has(candidate.parentId)) {
                    candidate.entity.reportsToId = candidate.parentId;
                    resolvedContactIds.add(candidate.entity.id);
                    childEntities.push(candidate.entity);
                    processedInCurrentPass = true;
                }
                else {
                    deferredCandidates.push(candidate);
                }
            }
            if (!processedInCurrentPass) {
                for (const candidate of deferredCandidates) {
                    candidate.entity.reportsToId = null;
                    childEntities.push(candidate.entity);
                    const meta = metaById.get(candidate.entity.id);
                    if (meta) {
                        meta.reportsToAttempted = candidate.parentId;
                        meta.issues.push(`Reports-to contact ${candidate.parentId} not found; stored without reports-to linkage.`);
                    }
                }
                break;
            }
            pendingCandidates = deferredCandidates;
        }
        if (childEntities.length > 0) {
            await this.contactRepository.save(childEntities);
        }
        const unprocessedRecords = [];
        for (const meta of metaById.values()) {
            if (meta.issues.length > 0) {
                unprocessedRecords.push({
                    ...meta.original,
                    message: meta.issues.join(' '),
                    accountAttempted: meta.accountAttempted,
                    companyFundAttempted: meta.companyFundAttempted,
                    reportsToAttempted: meta.reportsToAttempted,
                });
            }
        }
        return {
            processed: records.length,
            unprocessedRecords,
        };
    }
};
exports.ContactsService = ContactsService;
exports.ContactsService = ContactsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jvs_contact_entity_1.JvsContact)),
    __param(1, (0, typeorm_1.InjectRepository)(jvs_account_entity_1.JvsAccount)),
    __param(2, (0, typeorm_1.InjectRepository)(jvs_company_fund_entity_1.JvsCompanyFund)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ContactsService);
//# sourceMappingURL=contacts.service.js.map