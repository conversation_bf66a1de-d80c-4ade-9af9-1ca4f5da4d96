import { VectorDetailService } from './vector-detail.service';
import { UpsertVectorDetailDto } from './dto/upsert-vector-detail.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class VectorDetailController {
    private readonly vectorDetailService;
    private static readonly validationPipe;
    constructor(vectorDetailService: VectorDetailService);
    upsertVectorDetails(payload: UpsertVectorDetailDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            kpiPerticularAttempted: string | null;
            vectorMasterAttempted: string | null;
        }[];
    }>>;
}
