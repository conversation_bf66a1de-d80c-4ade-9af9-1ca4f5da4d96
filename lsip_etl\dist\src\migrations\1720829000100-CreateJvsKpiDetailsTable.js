"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJvsKpiDetailsTable1720829000100 = void 0;
class CreateJvsKpiDetailsTable1720829000100 {
    name = 'CreateJvsKpiDetailsTable1720829000100';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_kpi_details (
              id character varying(18) NOT NULL,
              name character varying(80),
              currencyisocode character varying(3),
              company__c character varying(18),
              aov_rs__c double precision,
              available__c boolean,
              average_order_value_product__c double precision,
              average_order_value__c double precision,
              business_unit__c character varying(18),
              cac_rs__c double precision,
              cac__c double precision,
              cm2__c double precision,
              cm3_of_nmv__c double precision,
              cm_in_localities_with_500_users__c double precision,
              cumulative_bank_accounts__c double precision,
              bank_accounts_created_per_month__c double precision,
              cash_in_bank__c double precision,
              category__c character varying(18),
              contribution_margin_product__c double precision,
              average_deposit_bank_account__c double precision,
              contribution_margin__c double precision,
              number_of_credit_card_issued__c double precision,
              customer_acquisiton_cost__c double precision,
              dau__c double precision,
              dtr_from_source__c double precision,
              date__c date,
              cash_burn_rs_cr__c double precision,
              cash_burn__c double precision,
              ebitda_rs_cr__c double precision,
              ebitda__c double precision,
              enable__c boolean,
              first_party_mix__c double precision,
              first_party__c double precision,
              fulfillment_cost_of_nmv__c double precision,
              gmv__c double precision,
              gmv_from_private_labels__c double precision,
              gmv_per_existing_buyer__c double precision,
              gmv_per_fos_cost__c double precision,
              gmv_per_new_buyer__c double precision,
              contribution_margin_rs_cr__c double precision,
              gross_margin__c double precision,
              gross_margin_of_nmv__c double precision,
              inventory_days__c double precision,
              latest_flag__c boolean,
              m12_gmv_retention__c double precision,
              m12_user_retention__c double precision,
              m1_repeat__c double precision,
              m2_gmv_retention__c double precision,
              contribution_margin_of_take_rate__c double precision,
              m2_user_retention__c double precision,
              m6_gmv_retention__c double precision,
              m6_user_retention__c double precision,
              discretionary_marketing__c double precision,
              gross_margin_product__c double precision,
              mau__c double precision,
              marketing_spends__c double precision,
              m2_retention__c double precision,
              marketplace_float__c double precision,
              merchant_acquisition_cost__c double precision,
              nmv__c double precision,
              nmv_per_buyer_rs__c double precision,
              no_of_active_trucks__c double precision,
              no_of_dense_lanes__c double precision,
              no_of_dense_localities__c double precision,
              no_of_orders__c double precision,
              no_of_paying_merchants__c double precision,
              no_of_tracked_trips__c double precision,
              no_of_transacting_buyers__c double precision,
              no_of_transacting_users__c double precision,
              no_of_transactions__c double precision,
              mac_rs__c double precision,
              number_of_buyers_number__c double precision,
              number_of_localities_with_500_users__c double precision,
              number_of_new_users__c double precision,
              number_of_transacting_users_lakh__c double precision,
              operating_cash_burn_rs_cr__c double precision,
              order_frequency__c double precision,
              payable_days__c double precision,
              paying_merchants__c double precision,
              people_g_a_others_rs_cr__c double precision,
              mau_lakh__c double precision,
              marketing__c double precision,
              receivable_days__c double precision,
              record_type_name__c character varying(1300),
              revenuetotal__c double precision,
              sales_cost__c double precision,
              shipping_cost__c double precision,
              shipping_cost_of_nmv__c double precision,
              take_rate_gmv__c double precision,
              take_rate_rs_cr__c double precision,
              take_rate__c double precision,
              take_rate_by_revenue_product__c double precision,
              take_rate_by_revenue__c double precision,
              take_rate_from_localities_with_500_user__c double precision,
              take_rate_per_user_rs__c double precision,
              take_rate_revenue_from_dense_localitie__c double precision,
              take_rate_revenue_per_existing_user__c double precision,
              take_rate_revenue_per_new_user__c double precision,
              test_kpi__c double precision,
              total_people_cost__c double precision,
              transacting_buyers__c double precision,
              transacting_stores_per_fos__c double precision,
              transacting_user_in_localities_with_500__c double precision,
              type_of_kpi__c character varying(255),
              working_capital__c double precision,
              key__c character varying(100),
              number_of_buyers__c double precision,
              revenue_rs_cr__c double precision,
              cm1__c double precision,
              customer_support_biz_ops_cost__c double precision,
              under_utilized_fixed_costs_in_operations__c double precision,
              tech_product_costs__c double precision,
              sg_a__c double precision,
              buyer_with_credit__c double precision,
              yim_12_months_after_dpd__c double precision,
              performance_marketing__c double precision,
              cm_rs_cr__c double precision,
              take_rate_of_localities_with_500_users__c double precision,
              performance_marketing_rs_cr__c double precision,
              cm_in_localities_500_users_rs_cr__c double precision,
              paying_users__c double precision,
              m3_paying_user_retention__c double precision,
              m3_paying_revenue_retention__c double precision,
              number_of_audio_heard_per_dau__c double precision,
              mo_arpu__c double precision,
              total_arpu__c double precision,
              cac_dollar__c double precision,
              m0_conversion__c double precision,
              conversion__c double precision,
              subscritpion_income__c double precision,
              marketing_cost__c double precision,
              employee_expense_india_us__c double precision,
              ebitda_dollar__c double precision,
              total_cashburn__c double precision,
              nmv_usd__c double precision,
              fmcg_nmv__c double precision,
              teman_ula_nmv__c double precision,
              others_nmv__c double precision,
              take_rate_net_margin__c double precision,
              take_rate_fmcg__c double precision,
              take_rate_terman_ula__c double precision,
              logistics_cost__c double precision,
              fos__c double precision,
              warehouse__c double precision,
              cash_in_bank_dollar__c double precision,
              unique_ordering_stores__c double precision,
              m6_store_retention__c double precision,
              nmv_per_store_per_month__c double precision,
              aov_dollar__c double precision,
              orders_per_store_per_month__c double precision,
              app_running_cost_server_analytics_cos__c double precision,
              marketing_cost_percent__c double precision,
              content_serving_cost__c double precision,
              other_branding_expenses__c double precision,
              music_license_cost__c double precision,
              employee_benefits_expenses__c double precision,
              others__c double precision,
              dau_mau__c double precision,
              retention_d30__c double precision,
              average_time_spent__c double precision,
              unique_creators__c double precision,
              total_installs__c double precision,
              organic__c double precision,
              cost_per_install__c double precision,
              revenue_dollar__c double precision,
              skilling__c double precision,
              recruitment__c double precision,
              marketing_others__c double precision,
              salary__c double precision,
              new_installs__c double precision,
              profile_complete__c double precision,
              uninstall_profile_completes__c double precision,
              screened_leads__c double precision,
              from_existing__c double precision,
              from_reactivated__c double precision,
              from_new__c double precision,
              screened_leads_mau__c double precision,
              m3_user_retention__c double precision,
              repeat_mau_to_life_to_date_user_base__c double precision,
              blended_cac_for_user__c double precision,
              new_employer_acquired__c double precision,
              active_employer__c double precision,
              jobs_activated__c double precision,
              m3_employer_retention__c double precision,
              repeat_employer_to_life_to_date_employer__c double precision,
              blended_cac_employer__c double precision,
              community_mau_of_app_mau__c double precision,
              community_to_community_m1_d30_d60_rete__c double precision,
              overall_enquiries_received_mtd_ytd__c double precision,
              conversion_rate_trailing_3_months__c double precision,
              new_orders_for_the_month__c double precision,
              gtv_arr_on_saas__c double precision,
              gmv_dollar__c double precision,
              take_rate_dollar__c double precision,
              delivered_gmv_for_the_month__c double precision,
              cumulative_open_order_book_closing__c double precision,
              cm_percent__c double precision,
              number_of_lsps_shippers_added__c double precision,
              active_shippers_lsps__c double precision,
              fixed_expenses__c double precision,
              gmv_from_repeat_customers__c double precision,
              new_customers_acquired__c double precision,
              active_customers__c double precision,
              q3_customer_retention__c double precision,
              active_suppliers__c double precision,
              q3_supplier_retention__c double precision,
              ar_value_in_rs_crs__c double precision,
              ap_value_in_rs_crs__c double precision,
              inventory_value_in_rs_crs__c double precision,
              net_wc_ar_ap_inv_adv__c double precision,
              wc_conversion_days__c double precision,
              free_cash__c double precision,
              ending_carr__c double precision,
              new_carr_upsell__c double precision,
              monthly_churn_downgrade__c double precision,
              no_of_customers_carr__c double precision,
              carr_per_customer__c double precision,
              ending_arr__c double precision,
              new_arr_upsell__c double precision,
              monthly_churn_downgrade_arr__c double precision,
              number_of_customers_arr__c double precision,
              arr_per_customer__c double precision,
              arr_as_a_of_carr__c double precision,
              platform__c double precision,
              cpaas__c double precision,
              voice__c double precision,
              services__c double precision,
              gross_profit__c double precision,
              operating_income__c double precision,
              dso__c double precision,
              net_retention__c double precision,
              average_carr_per_new_customer__c double precision,
              carr_fte__c double precision,
              arr_fte__c double precision,
              annualised_net_churn__c double precision,
              rule_of_40__c double precision,
              net_magic_number__c double precision,
              sales_cost_000__c double precision,
              net_revenue__c double precision,
              other_revenue_e_g_shipping_revenue_etc__c double precision,
              cogs__c double precision,
              all_discounts_refunds_etc__c double precision,
              wastage__c double precision,
              parking_fuel_last_mile__c double precision,
              packaging_cost__c double precision,
              hub_operations_cost__c double precision,
              cm2_percentage_of_netrevenue__c double precision,
              marketing_cost_percentage_of_netrevenue__c double precision,
              cm3_percent_of_net_revenue__c double precision,
              number_of_orders__c double precision,
              existing_buyers__c double precision,
              orders_per_buyer__c double precision,
              existing_buyers_no_buyer_permonth__c double precision,
              no_of_unique_buyers__c double precision,
              existing_buyers_000__c double precision,
              gmv_from_existing_buyers__c double precision,
              m3_gmv_retention__c double precision,
              gmv_from_top_3_categories__c double precision,
              gross_booking__c double precision,
              new_expansion__c double precision,
              downgrade_churn__c double precision,
              ndr__c double precision,
              sales_marketing__c double precision,
              general_administrative__c double precision,
              research_development__c double precision,
              net_burn_000__c double precision,
              burn_multiple__c double precision,
              avg_new_deal_size__c double precision,
              event_created__c double precision,
              event_hosted__c double precision,
              events_with_less_than_500_participants__c double precision,
              events_with_500_to_1000_participants__c double precision,
              events_with_greater_1000_participants__c double precision,
              roi_on_marketing_spends__c double precision,
              roi_on_sales_spends__c double precision,
              sales_quota_attainment__c double precision,
              people_related_spend__c double precision,
              cash_burn_dollar_000__c double precision,
              net_dollar_retention__c double precision,
              gross_retention__c double precision,
              net_seat_retention__c double precision,
              gross_seat_retention__c double precision,
              logo_retention__c double precision,
              total_s_m_spend__c double precision,
              s_m_efficiency__c double precision,
              new_logo_arr__c double precision,
              new_logo_seats__c double precision,
              new_logo__c double precision,
              new_logo_asp__c double precision,
              new_logo_avg_seats__c double precision,
              ebitda_burn__c double precision,
              total_bookings__c double precision,
              global__c double precision,
              smb__c double precision,
              mm__c double precision,
              enterprise__c double precision,
              invoiced_revenue__c double precision,
              gm__c double precision,
              smb_percent__c double precision,
              mm_percent__c double precision,
              enterprise_percent__c double precision,
              ml_to_mql__c double precision,
              mql_to_sql__c double precision,
              sql_to_closed__c double precision,
              gross_roi__c double precision,
              outbound_quota_attainment__c double precision,
              enterprise_mm_cac__c double precision,
              smb_inbound_marketing_cac__c double precision,
              saas_logo_q1_retention__c double precision,
              saas_revenue_q1_retention__c double precision,
              saas_conversion_self_serve_funnel__c double precision,
              marketplace_enterprise_logo_q1_reten__c double precision,
              marketplace_enterprise_revenue_q1_re__c double precision,
              marketplace_mm_logo_q1_retention__c double precision,
              marketplace_mm_revenue_q1_retention__c double precision,
              creator_earnings__c double precision,
              editor_earnings__c double precision,
              cm1_percent__c double precision,
              inbound_quota_attainment__c double precision,
              m3_spend_per_customer__c double precision,
              m3_m0_spend_per_customer__c double precision,
              npas__c double precision,
              money_remitted_to_india_on_zolve__c double precision,
              discretionary_marketing_rs_cr__c double precision,
              take_rate_others__c double precision,
              subs_revenue__c double precision,
              subs_hosting_cost__c double precision,
              subs_client_support_cost__c double precision,
              arr_carr__c double precision,
              iarr_contracted__c double precision,
              employees_on_platform_contracted__c double precision,
              average_acv__c double precision,
              trading_volume__c double precision,
              trading_revenue__c double precision,
              cumulative_downloads__c double precision,
              cumulative_kyced_users__c double precision,
              number_of_active_traders__c double precision,
              trader_w24_retention__c double precision,
              average_trading_frequency_per_trader__c double precision,
              total_contracts_value_arr__c double precision,
              total_terminal_arr__c double precision,
              terminal_arr_net_of_discounts__c double precision,
              terminal_arr_added__c double precision,
              terminal_arr_lost__c double precision,
              non_recurring_revenue__c double precision,
              total_institutes__c double precision,
              number_of_new_institutes__c double precision,
              terminal_arr_institute__c double precision,
              s_m_efficiency_net_of_discounts__c double precision,
              total_creators__c double precision,
              number_of_new_creators__c double precision,
              total_customers__c double precision,
              terminal_arr_lost_dollar__c double precision,
              q2_trip_retention__c double precision,
              m6_lsp_reten_adjusted_for_pilot_trail__c double precision,
              m6_shipper_retention_adjusted_trials__c double precision,
              number_of_suppliers_added__c double precision,
              aum_total__c double precision,
              gross_profit_percent__c double precision,
              aum_cash__c double precision,
              aum_cpf__c double precision,
              aum_srs__c double precision,
              new_investment__c double precision,
              redemption__c double precision,
              net_new_money_invested__c double precision,
              existing_clients__c double precision,
              new_clients__c double precision,
              registered_accounts__c double precision,
              broker_accounts__c double precision,
              funded_clients__c double precision,
              churned_clients_from_company__c double precision,
              share_of_peer_referrals__c double precision,
              recurring_amount__c double precision,
              gm_client__c double precision,
              payback_period_on_cac__c double precision,
              m6_median_net_invested__c double precision,
              cm_percent_revenue__c double precision,
              expansion_arr__c double precision,
              expansion_arr_overall_arr__c double precision,
              headcount__c double precision,
              gross_margin_dollar__c double precision,
              income__c double precision,
              arr_cac__c double precision,
              churn__c double precision,
              ltv_cac__c double precision,
              CONSTRAINT pk_jvs_kpi_details_id PRIMARY KEY (id)
            );
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_details_company__c ON jvs_kpi_details (company__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_details_business_unit__c ON jvs_kpi_details (business_unit__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_details_category__c ON jvs_kpi_details (category__c);
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_details_date__c ON jvs_kpi_details (date__c);
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_details ADD CONSTRAINT fk_jvs_kpi_details_company__c FOREIGN KEY (company__c) REFERENCES jvs_accounts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_details ADD CONSTRAINT fk_jvs_kpi_details_business_unit__c FOREIGN KEY (business_unit__c) REFERENCES jvs_business_units(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_details DROP CONSTRAINT IF EXISTS fk_jvs_kpi_details_business_unit__c;
    `);
        await queryRunner.query(`
      ALTER TABLE jvs_kpi_details DROP CONSTRAINT IF EXISTS fk_jvs_kpi_details_company__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_details_date__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_details_category__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_details_business_unit__c;
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_details_company__c;
    `);
        await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_kpi_details;
    `);
    }
}
exports.CreateJvsKpiDetailsTable1720829000100 = CreateJvsKpiDetailsTable1720829000100;
//# sourceMappingURL=1720829000100-CreateJvsKpiDetailsTable.js.map