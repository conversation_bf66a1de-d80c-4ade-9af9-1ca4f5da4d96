import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { KpiRelevancyRecordDto } from './upsert-kpi-relevancy.dto';
export declare class UnprocessedKpiRelevancyRecordDto extends KpiRelevancyRecordDto {
    message: string;
    companyAttempted: string | null;
    industryMasterAttempted: string | null;
    kpiMasterAttempted: string | null;
    subIndustryAttempted: string | null;
}
export declare class KpiRelevancyProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedKpiRelevancyRecordDto[];
}
export declare class KpiRelevancyApiResponseDto extends BaseApiResponseDto {
    data: KpiRelevancyProcessedDataDto;
}
