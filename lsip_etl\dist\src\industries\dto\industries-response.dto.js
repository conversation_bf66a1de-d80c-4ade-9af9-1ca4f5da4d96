"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndustriesApiResponseDto = exports.IndustriesProcessedDataDto = exports.UnprocessedIndustryRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_industries_dto_1 = require("./upsert-industries.dto");
class UnprocessedIndustryRecordDto extends upsert_industries_dto_1.IndustryRecordDto {
    message;
    parentIndustryAttempted;
}
exports.UnprocessedIndustryRecordDto = UnprocessedIndustryRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedIndustryRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Parent industry identifier that was provided for the record.',
        nullable: true,
        maxLength: 18,
    }),
    __metadata("design:type", Object)
], UnprocessedIndustryRecordDto.prototype, "parentIndustryAttempted", void 0);
class IndustriesProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.IndustriesProcessedDataDto = IndustriesProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records persisted.' }),
    __metadata("design:type", Number)
], IndustriesProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records that could not be fully processed (e.g., missing parent linkage).',
        type: () => [UnprocessedIndustryRecordDto],
    }),
    __metadata("design:type", Array)
], IndustriesProcessedDataDto.prototype, "unprocessedRecords", void 0);
class IndustriesApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.IndustriesApiResponseDto = IndustriesApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: IndustriesProcessedDataDto }),
    __metadata("design:type", IndustriesProcessedDataDto)
], IndustriesApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=industries-response.dto.js.map