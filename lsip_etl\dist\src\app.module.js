"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const config_module_1 = require("./config/config.module");
const database_module_1 = require("./database/database.module");
const salesforce_module_1 = require("./salesforce/salesforce.module");
const industries_module_1 = require("./industries/industries.module");
const accounts_module_1 = require("./accounts/accounts.module");
const company_funds_module_1 = require("./company-funds/company-funds.module");
const contacts_module_1 = require("./contacts/contacts.module");
const business_units_module_1 = require("./business-units/business-units.module");
const business_ratings_module_1 = require("./business-ratings/business-ratings.module");
const founder_ratings_module_1 = require("./founder-ratings/founder-ratings.module");
const kpi_master_module_1 = require("./kpi-master/kpi-master.module");
const kpi_perticular_module_1 = require("./kpi-perticular/kpi-perticular.module");
const kpi_plan_actual_module_1 = require("./kpi-plan-actual/kpi-plan-actual.module");
const kpi_details_module_1 = require("./kpi-details/kpi-details.module");
const kpi_relevancy_module_1 = require("./kpi-relevancy/kpi-relevancy.module");
const vector_master_module_1 = require("./vector-master/vector-master.module");
const vector_detail_module_1 = require("./vector-detail/vector-detail.module");
const vector_plan_actual_module_1 = require("./vector-plan-actual/vector-plan-actual.module");
const records_module_1 = require("./records/records.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_module_1.ConfigModule,
            database_module_1.DatabaseModule,
            salesforce_module_1.SalesforceModule,
            industries_module_1.IndustriesModule,
            accounts_module_1.AccountsModule,
            company_funds_module_1.CompanyFundsModule,
            contacts_module_1.ContactsModule,
            business_units_module_1.BusinessUnitsModule,
            business_ratings_module_1.BusinessRatingsModule,
            founder_ratings_module_1.FounderRatingsModule,
            kpi_master_module_1.KpiMasterModule,
            kpi_perticular_module_1.KpiPerticularModule,
            kpi_plan_actual_module_1.KpiPlanActualModule,
            kpi_details_module_1.KpiDetailsModule,
            kpi_relevancy_module_1.KpiRelevancyModule,
            vector_master_module_1.VectorMasterModule,
            vector_detail_module_1.VectorDetailModule,
            vector_plan_actual_module_1.VectorPlanActualModule,
            records_module_1.RecordsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map