import { FounderRatingsService } from './founder-ratings.service';
import { UpsertFounderRatingsDto } from './dto/upsert-founder-ratings.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class FounderRatingsController {
    private readonly founderRatingsService;
    private static readonly validationPipe;
    constructor(founderRatingsService: FounderRatingsService);
    upsertFounderRatings(payload: UpsertFounderRatingsDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            accountAttempted: string | null;
        }[];
    }>>;
}
