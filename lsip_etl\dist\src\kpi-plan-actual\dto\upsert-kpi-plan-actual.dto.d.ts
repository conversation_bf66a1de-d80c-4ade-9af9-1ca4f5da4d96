export declare class KpiPlanActualRecordDto {
    id: string;
    name?: string;
    actual__c?: string;
    kpi_particular__c?: string;
    month1__c?: string;
    name__c?: string;
    number_of_plan_revisions__c?: number;
    plan__c?: string;
    quarter__c?: string;
    reason_for_deviation_remarks__c?: string;
    year__c?: string;
    actual_formula__c?: string;
    month_number__c?: number;
    percentage_of_achievement__c?: number;
    consolidated_date__c?: string;
    plan_actual_date__c?: string;
}
export declare class UpsertKpiPlanActualDto {
    KpiPlanActuals: KpiPlanActualRecordDto[];
}
export type KpiPlanActualRecord = KpiPlanActualRecordDto;
