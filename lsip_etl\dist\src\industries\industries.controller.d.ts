import { IndustriesService } from './industries.service';
import { UpsertIndustriesDto } from './dto/upsert-industries.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class IndustriesController {
    private readonly industriesService;
    private static readonly validationPipe;
    constructor(industriesService: IndustriesService);
    upsertIndustries(payload: UpsertIndustriesDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            id: string;
            name: string | null;
            parentIndustryAttempted: string | null;
            message: string;
            frequency__c?: string;
            parent_industry__c?: string;
        }[];
    }>>;
}
