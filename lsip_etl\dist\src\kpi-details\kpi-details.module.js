"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiDetailsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const kpi_details_controller_1 = require("./kpi-details.controller");
const kpi_details_service_1 = require("./kpi-details.service");
const jvs_kpi_detail_entity_1 = require("./jvs-kpi-detail.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
const jvs_business_unit_entity_1 = require("../business-units/jvs-business-unit.entity");
let KpiDetailsModule = class KpiDetailsModule {
};
exports.KpiDetailsModule = KpiDetailsModule;
exports.KpiDetailsModule = KpiDetailsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_kpi_detail_entity_1.JvsKpiDetail, jvs_account_entity_1.JvsAccount, jvs_business_unit_entity_1.JvsBusinessUnit])],
        controllers: [kpi_details_controller_1.KpiDetailsController],
        providers: [kpi_details_service_1.KpiDetailsService],
        exports: [kpi_details_service_1.KpiDetailsService],
    })
], KpiDetailsModule);
//# sourceMappingURL=kpi-details.module.js.map