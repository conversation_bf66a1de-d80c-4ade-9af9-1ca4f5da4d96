export declare class JvsBusinessRating {
    id: string;
    name: string | null;
    createdDate: Date | null;
    accountId: string | null;
    productVisionStrategy: string | null;
    productVisionStrategyDescription: string | null;
    gtmMuscleEntSalesPartnershipDescription: string | null;
    gtmMuscleEntSalesPartnership: string | null;
    dataTechnologyOrientation: string | null;
    dataTechnologyOrientationDescription: string | null;
    deepTechEnggProductLeadership: string | null;
    deepTechEnggProductLeadershipDescription: string | null;
    gtmMuscleCommercialization: string | null;
    gtmMuscleCommercializationDescription: string | null;
    regulatory: string | null;
    regulatoryDescription: string | null;
    productVisionStrategyAiMl: string | null;
    productVisionStrategyAiMlDescription: string | null;
    gtmMuscleForDevtoolsProsumer: string | null;
    gtmMuscleForDevtoolsProsumerDescription: string | null;
    narrativeBuilding: string | null;
    narrativeBuildingDescription: string | null;
    productVisionStrategyAiLlm: string | null;
    productVisionStrategyAiLlmDescription: string | null;
    gtmMuscle: string | null;
    gtmMuscleDescription: string | null;
    positioningNarrativeBuilding: string | null;
    positioningNarrativeBuildingDescription: string | null;
    productStrategyStrategicThinking: string | null;
    productStrategyStrategicThinkingDescription: string | null;
    marketingNarrativeBuilding: string | null;
    marketingNarrativeBuildingDescription: string | null;
    aiCapability: string | null;
    aiCapabilityDescription: string | null;
    categoryCreation: string | null;
    categoryCreationDescription: string | null;
}
