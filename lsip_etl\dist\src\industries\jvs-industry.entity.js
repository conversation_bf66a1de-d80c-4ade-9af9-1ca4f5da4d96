"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JvsIndustry = void 0;
const typeorm_1 = require("typeorm");
let JvsIndustry = class JvsIndustry {
    id;
    name = null;
    parentIndustryId = null;
    frequency = null;
};
exports.JvsIndustry = JvsIndustry;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 18 }),
    __metadata("design:type", String)
], JvsIndustry.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 80, nullable: true }),
    __metadata("design:type", Object)
], JvsIndustry.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'parent_industry__c', type: 'varchar', length: 18, nullable: true }),
    __metadata("design:type", Object)
], JvsIndustry.prototype, "parentIndustryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'frequency__c', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", Object)
], JvsIndustry.prototype, "frequency", void 0);
exports.JvsIndustry = JvsIndustry = __decorate([
    (0, typeorm_1.Entity)({ name: 'jvs_industries' })
], JvsIndustry);
//# sourceMappingURL=jvs-industry.entity.js.map