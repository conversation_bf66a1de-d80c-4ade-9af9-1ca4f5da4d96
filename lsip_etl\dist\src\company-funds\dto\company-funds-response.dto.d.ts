import { BaseApiResponseDto } from '../../common/dto/api-response.dto';
import { CompanyFundRecordDto } from './upsert-company-funds.dto';
export declare class UnprocessedCompanyFundRecordDto extends CompanyFundRecordDto {
    message: string;
    accountAttempted: string | null;
}
export declare class CompanyFundsProcessedDataDto {
    processed: number;
    unprocessedRecords: UnprocessedCompanyFundRecordDto[];
}
export declare class CompanyFundsApiResponseDto extends BaseApiResponseDto {
    data: CompanyFundsProcessedDataDto;
}
