import { KpiDetailsService } from './kpi-details.service';
import { UpsertKpiDetailsDto } from './dto/upsert-kpi-details.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
export declare class KpiDetailsController {
    private readonly kpiDetailsService;
    private static readonly validationPipe;
    constructor(kpiDetailsService: KpiDetailsService);
    upsertKpiDetails(payload: UpsertKpiDetailsDto): Promise<ApiResponse<{
        processed: number;
        unprocessedRecords: {
            message: string;
            companyAttempted: string | null;
            businessUnitAttempted: string | null;
        }[];
    }>>;
}
