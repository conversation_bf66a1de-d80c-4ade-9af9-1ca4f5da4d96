"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SwaggerConfig = exports.sanitizeSwaggerDocument = exports.swaggerJsonPath = exports.swaggerDocPath = void 0;
const swagger_1 = require("@nestjs/swagger");
exports.swaggerDocPath = 'api/docs';
exports.swaggerJsonPath = '/api/docs-json';
const sanitizeString = (value) => value.replace(/[\u2028\u2029]/g, ' ');
const sanitizeSwaggerDocument = (document) => {
    const seen = new WeakSet();
    const sanitize = (value) => {
        if (typeof value === 'string') {
            return sanitizeString(value);
        }
        if (Array.isArray(value)) {
            return value.map((item) => sanitize(item));
        }
        if (value && typeof value === 'object') {
            if (seen.has(value)) {
                return value;
            }
            seen.add(value);
            for (const key of Object.keys(value)) {
                const current = value[key];
                value[key] = sanitize(current);
            }
        }
        return value;
    };
    return sanitize(document);
};
exports.sanitizeSwaggerDocument = sanitizeSwaggerDocument;
class SwaggerConfig {
    static setup(app) {
        const swaggerConfig = new swagger_1.DocumentBuilder()
            .setTitle('LSIP ETL API')
            .setDescription('API documentation for ETL endpoints.')
            .setVersion('1.0')
            .addApiKey({
            type: 'apiKey',
            name: 'x-api-key',
            in: 'header',
            description: 'API key for sync operations',
        }, 'api-key')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, swaggerConfig);
        const sanitizedDocument = (0, exports.sanitizeSwaggerDocument)(document);
        swagger_1.SwaggerModule.setup(exports.swaggerDocPath, app, sanitizedDocument, {
            swaggerUrl: exports.swaggerJsonPath,
            swaggerOptions: {
                url: exports.swaggerJsonPath,
                persistAuthorization: true,
                tagsSorter: 'alpha',
                operationsSorter: 'alpha',
            },
            customSiteTitle: 'LSIP ETL API Doc',
            customfavIcon: '/favicon.ico',
            customCssUrl: '/swagger-ui.css',
        });
    }
}
exports.SwaggerConfig = SwaggerConfig;
//# sourceMappingURL=swagger.config.js.map