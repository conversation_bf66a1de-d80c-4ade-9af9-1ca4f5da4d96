"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("@nestjs/config");
exports.default = (0, config_1.registerAs)('database', () => ({
    host: process.env.DB_HOST ?? 'localhost',
    port: Number(process.env.DB_PORT ?? 5432),
    username: process.env.DB_USER ?? 'postgres',
    password: process.env.DB_PASS ?? '',
    name: process.env.DB_NAME ?? 'postgres',
    sslMode: process.env.DB_SSL_MODE ?? 'require',
    schema: process.env.DB_SCHEMA,
}));
//# sourceMappingURL=database.config.js.map