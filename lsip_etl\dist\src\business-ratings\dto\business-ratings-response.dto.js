"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessRatingsApiResponseDto = exports.BusinessRatingsProcessedDataDto = exports.UnprocessedBusinessRatingRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const api_response_dto_1 = require("../../common/dto/api-response.dto");
const upsert_business_ratings_dto_1 = require("./upsert-business-ratings.dto");
class UnprocessedBusinessRatingRecordDto extends upsert_business_ratings_dto_1.BusinessRatingRecordDto {
    message;
    accountAttempted;
}
exports.UnprocessedBusinessRatingRecordDto = UnprocessedBusinessRatingRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason the record could not be fully processed.' }),
    __metadata("design:type", String)
], UnprocessedBusinessRatingRecordDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account identifier that could not be resolved.', nullable: true, maxLength: 18 }),
    __metadata("design:type", Object)
], UnprocessedBusinessRatingRecordDto.prototype, "accountAttempted", void 0);
class BusinessRatingsProcessedDataDto {
    processed;
    unprocessedRecords;
}
exports.BusinessRatingsProcessedDataDto = BusinessRatingsProcessedDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of records processed.' }),
    __metadata("design:type", Number)
], BusinessRatingsProcessedDataDto.prototype, "processed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Records that were persisted with warnings.',
        type: () => [UnprocessedBusinessRatingRecordDto],
    }),
    __metadata("design:type", Array)
], BusinessRatingsProcessedDataDto.prototype, "unprocessedRecords", void 0);
class BusinessRatingsApiResponseDto extends api_response_dto_1.BaseApiResponseDto {
}
exports.BusinessRatingsApiResponseDto = BusinessRatingsApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: BusinessRatingsProcessedDataDto }),
    __metadata("design:type", BusinessRatingsProcessedDataDto)
], BusinessRatingsApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=business-ratings-response.dto.js.map