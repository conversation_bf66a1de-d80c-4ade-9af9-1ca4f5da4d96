"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessRatingsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const business_ratings_controller_1 = require("./business-ratings.controller");
const business_ratings_service_1 = require("./business-ratings.service");
const jvs_business_rating_entity_1 = require("./jvs-business-rating.entity");
const jvs_account_entity_1 = require("../accounts/jvs-account.entity");
let BusinessRatingsModule = class BusinessRatingsModule {
};
exports.BusinessRatingsModule = BusinessRatingsModule;
exports.BusinessRatingsModule = BusinessRatingsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([jvs_business_rating_entity_1.JvsBusinessRating, jvs_account_entity_1.JvsAccount])],
        controllers: [business_ratings_controller_1.BusinessRatingsController],
        providers: [business_ratings_service_1.BusinessRatingsService],
        exports: [business_ratings_service_1.BusinessRatingsService],
    })
], BusinessRatingsModule);
//# sourceMappingURL=business-ratings.module.js.map